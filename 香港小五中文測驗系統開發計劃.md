# 香港小五中文測驗系統開發計劃

## 專案概述

### 目標用戶
- **主要用戶**: 香港小五學生
- **次要用戶**: 中文科老師
- **使用目的**: 協助學生準備小五中文科呈分試

### 設備支援
- **主要設備**: 平板電腦、手機
- **次要設備**: 桌面電腦
- **設計重點**: 響應式設計，優先考慮移動端體驗

## 系統架構

### 整體架構 (純前端方案)
```
香港小五中文測驗系統 (Static Web App)
├── 前端應用 (Frontend SPA)
│   ├── 學生測驗介面
│   ├── 老師管理介面
│   └── 題庫管理介面
├── 資料存儲 (Client-side Storage)
│   ├── 題庫 (JSON 檔案)
│   ├── 本地存儲 (LocalStorage)
│   └── 瀏覽器資料庫 (IndexedDB)
├── 靜態資源 (Static Assets)
│   ├── HTML/CSS/JavaScript
│   ├── 圖片和媒體檔案
│   └── 題庫 JSON 檔案
└── 部署環境
    └── Netlify (靜態網站託管)
```

### 技術架構建議 (Netlify 優化)
- **前端框架**: React.js 或 Vue.js (支援靜態生成)
- **建構工具**: Vite 或 Create React App
- **資料存儲**: JSON 檔案 + LocalStorage + IndexedDB
- **樣式框架**: Tailwind CSS (參考原設計)
- **響應式設計**: Mobile-first 設計原則
- **部署平台**: Netlify (自動部署、CDN、HTTPS)
- **版本控制**: Git + GitHub (連接 Netlify)

## 題型分析與設計

### 現有題型 (參考 Reference)
1. **詞語選擇** ✅
   - 成語運用
   - 詞語辨析
   - 分值: 每題2分

2. **量詞運用** ✅
   - 物量詞/動量詞選擇
   - 量詞類型判斷
   - 分值: 每個答案1分

3. **句子排序** ✅
   - 拖拽互動功能
   - 多句子邏輯排列
   - 分值: 4分

4. **重組句子** ✅
   - 詞語拖拽組合
   - 語法結構訓練
   - 分值: 4分

5. **標點符號** ✅
   - 即時預覽功能
   - 標點符號選擇
   - 分值: 6分

6. **閱讀理解** ✅
   - 響應式文章+問題佈局
   - 多種問答形式
   - 分值: 8分

### 需要補充的題型
7. **修辭手法辨析** ❌
   - 比喻、擬人、排比等
   - 修辭手法識別與運用

8. **句式變換** ❌
   - 陳述句改疑問句
   - 感嘆句轉換
   - 句式仿作

9. **改寫句子** ❌
   - 縮句練習
   - 擴句練習
   - 句子改寫

10. **字形辨識** ❌
    - 形近字辨別
    - 同音字選擇
    - 錯別字糾正

11. **詞語辨析進階** ❌
    - 近義詞選擇
    - 反義詞配對
    - 詞語配搭

12. **段意理解** ❌
    - 段落大意歸納
    - 文章結構分析

## 系統功能模組

### 1. 題庫管理系統

#### 資料庫 ER Diagram
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   QuestionType  │    │    Question     │    │     Option      │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ type_id (PK)    │◄──┤ question_id(PK) │    │ option_id (PK)  │
│ type_name       │   │ type_id (FK)    │───►│ question_id(FK) │
│ description     │   │ difficulty      │    │ option_text     │
│ template_config │   │ points          │    │ is_correct      │
│ created_at      │   │ question_text   │    │ order_index     │
│ updated_at      │   │ correct_answer  │    │ created_at      │
└─────────────────┘   │ explanation     │    └─────────────────┘
                      │ tags            │
┌─────────────────┐   │ created_by      │    ┌─────────────────┐
│      User       │   │ created_at      │    │      Paper      │
├─────────────────┤   │ updated_at      │    ├─────────────────┤
│ user_id (PK)    │──►│ status          │◄──┤ paper_id (PK)   │
│ username        │   └─────────────────┘   │ paper_name      │
│ email           │                         │ created_by (FK) │
│ password_hash   │   ┌─────────────────┐   │ total_points    │
│ role            │   │   PaperQuestion │   │ time_limit      │
│ created_at      │   ├─────────────────┤   │ instructions    │
│ updated_at      │   │ paper_id (FK)   │◄──┤ created_at      │
│ last_login      │   │ question_id(FK) │   │ updated_at      │
└─────────────────┘   │ order_index     │   │ status          │
                      │ custom_points   │   └─────────────────┘
┌─────────────────┐   └─────────────────┘
│   TestSession   │
├─────────────────┤   ┌─────────────────┐   ┌─────────────────┐
│ session_id (PK) │   │  StudentAnswer  │   │   TestResult    │
│ student_id (FK) │──►├─────────────────┤   ├─────────────────┤
│ paper_id (FK)   │   │ answer_id (PK)  │   │ result_id (PK)  │
│ start_time      │   │ session_id (FK) │◄──┤ session_id (FK) │
│ end_time        │   │ question_id(FK) │   │ total_score     │
│ status          │   │ student_answer  │   │ max_score       │
│ created_at      │   │ is_correct      │   │ percentage      │
└─────────────────┘   │ points_earned   │   │ time_taken      │
                      │ answered_at     │   │ completed_at    │
                      └─────────────────┘   │ feedback        │
                                           └─────────────────┘
```

#### 詳細資料結構設計

##### 1. 題型分類 (QuestionType)
```json
{
  "type_id": "word_choice",
  "type_name": "詞語選擇",
  "description": "成語運用、詞語辨析等選擇題",
  "template_config": {
    "input_type": "radio",
    "max_options": 4,
    "supports_images": false,
    "supports_audio": false,
    "default_points": 2
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

##### 2. 題目主體 (Question)
```json
{
  "question_id": "q_word_001",
  "type_id": "word_choice",
  "difficulty": "intermediate",
  "points": 2,
  "question_text": "我有疼愛我的爸爸、媽媽，還有一個感情濃厚、關係密切的好哥哥。",
  "question_context": {
    "instruction": "請選擇最適合的詞語填入空格",
    "highlight_text": "感情濃厚、關係密切",
    "replacement_target": "______"
  },
  "correct_answer": "C",
  "explanation": "「相親相愛」指家人之間感情深厚，彼此關愛，最符合句子描述的家庭關係。",
  "tags": ["成語運用", "家庭關係", "情感表達"],
  "metadata": {
    "source": "小五中文課本第三課",
    "curriculum_code": "P5_C3_01",
    "estimated_time": 60
  },
  "created_by": "teacher_001",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "status": "active"
}
```

##### 3. 選項設計 (Option)
```json
[
  {
    "option_id": "opt_001_A",
    "question_id": "q_word_001",
    "option_text": "相依為命",
    "is_correct": false,
    "order_index": 1,
    "explanation": "指互相依靠生活，通常用於困難處境",
    "created_at": "2024-01-01T00:00:00Z"
  },
  {
    "option_id": "opt_001_B",
    "question_id": "q_word_001",
    "option_text": "相親相愛",
    "is_correct": true,
    "order_index": 2,
    "explanation": "指彼此親近友愛，最符合題意",
    "created_at": "2024-01-01T00:00:00Z"
  }
]
```

##### 4. 特殊題型資料結構

###### 4.1 句子排序題
```json
{
  "question_id": "q_order_001",
  "type_id": "sentence_ordering",
  "question_text": "請將下列句子按正確順序排列",
  "question_data": {
    "sentences": [
      {"id": "A", "text": "無拘無束地談天說地。"},
      {"id": "B", "text": "漣漪在陽光的照耀下，"},
      {"id": "C", "text": "我和表哥坐在湖邊的草地上，"}
    ],
    "correct_order": ["C", "G", "F", "E", "B", "D", "H", "A"]
  },
  "interaction_config": {
    "drag_enabled": true,
    "click_to_select": true,
    "show_preview": false
  }
}
```

###### 4.2 標點符號題
```json
{
  "question_id": "q_punct_001",
  "type_id": "punctuation",
  "question_text": "請為下列句子選擇最適合的標點符號",
  "question_data": {
    "sentence": "春天的時候，花園裏繁花似錦[ 1 ]冬天的時候，花園裏枝葉零落[ 2 ]",
    "punctuation_slots": [
      {
        "slot_id": 1,
        "position": 15,
        "options": ["，", "；", "。"],
        "correct": "；"
      },
      {
        "slot_id": 2,
        "position": 30,
        "options": ["，", "；", "。"],
        "correct": "。"
      }
    ]
  },
  "display_config": {
    "show_preview": true,
    "highlight_slots": true
  }
}
```

###### 4.3 閱讀理解題
```json
{
  "question_id": "q_reading_001",
  "type_id": "reading_comprehension",
  "question_data": {
    "article": {
      "title": "斯特拉斯堡的聖誕",
      "content": "去年冬天，爸爸媽媽帶我到法國的斯特拉斯堡旅遊...",
      "word_count": 450,
      "reading_time": 300
    },
    "questions": [
      {
        "sub_question_id": "q_reading_001_1",
        "question_text": "從文中找出適當的詞語，使句子的意思完整",
        "question_type": "fill_blank",
        "points": 2,
        "correct_answer": "屹立"
      },
      {
        "sub_question_id": "q_reading_001_2",
        "question_text": "為甚麼「我」在斯特拉斯堡大教堂裏不敢做聲？",
        "question_type": "short_answer",
        "points": 4,
        "sample_answers": ["教堂氣氛莊嚴神聖", "身處其中感到肅穆"],
        "keywords": ["莊嚴", "神聖", "肅穆", "安靜"]
      }
    ]
  },
  "layout_config": {
    "mobile_layout": "vertical_split",
    "desktop_layout": "horizontal_split",
    "article_position": "left"
  }
}
```

#### 題目屬性詳細說明

##### 基本屬性
- **question_id**: 唯一識別碼 (格式: q_{type}_{sequence})
- **type_id**: 題型分類ID，對應QuestionType表
- **difficulty**: 難度等級 (beginner/intermediate/advanced)
- **points**: 題目分值 (1-10分)
- **status**: 題目狀態 (active/inactive/draft/archived)

##### 內容屬性
- **question_text**: 題目主要內容
- **question_context**: 題目上下文和說明
- **correct_answer**: 標準答案
- **explanation**: 答案解析
- **question_data**: 特殊題型的結構化數據

##### 分類屬性
- **tags**: 標籤陣列，便於篩選和搜尋
- **metadata**: 元數據 (來源、課程代碼、預估時間等)
- **curriculum_code**: 課程編碼，對應教學大綱

##### 管理屬性
- **created_by**: 創建者ID
- **created_at**: 創建時間
- **updated_at**: 最後更新時間
- **version**: 版本號 (支援題目版本控制)

#### 題庫索引設計
```json
{
  "indexes": [
    {"fields": ["type_id", "difficulty"], "name": "type_difficulty_idx"},
    {"fields": ["tags"], "name": "tags_idx"},
    {"fields": ["created_by", "created_at"], "name": "creator_time_idx"},
    {"fields": ["status", "updated_at"], "name": "status_update_idx"},
    {"fields": ["curriculum_code"], "name": "curriculum_idx"}
  ]
}
```

### 2. 試卷生成器

#### 資料結構設計

##### 試卷模板 (Paper)
```json
{
  "paper_id": "paper_001",
  "paper_name": "小五中文期中測驗",
  "description": "涵蓋詞語運用、閱讀理解等題型",
  "created_by": "teacher_001",
  "paper_config": {
    "total_points": 36,
    "time_limit": 60,
    "question_count": 15,
    "difficulty_distribution": {
      "beginner": 40,
      "intermediate": 45,
      "advanced": 15
    },
    "type_distribution": {
      "word_choice": 6,
      "quantifier": 4,
      "sentence_ordering": 4,
      "punctuation": 6,
      "reading_comprehension": 8,
      "rhetoric": 4,
      "sentence_transformation": 4
    }
  },
  "instructions": "請仔細閱讀題目，選擇最適合的答案。",
  "layout_config": {
    "show_progress": true,
    "allow_review": true,
    "randomize_options": false,
    "pagination": true
  },
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z",
  "status": "active"
}
```

##### 試卷題目關聯 (PaperQuestion)
```json
{
  "paper_id": "paper_001",
  "question_id": "q_word_001",
  "order_index": 1,
  "section_name": "一、詞語選擇",
  "custom_points": 2,
  "custom_instruction": "請選擇最適合的詞語",
  "display_config": {
    "show_explanation": false,
    "allow_skip": true,
    "time_limit": 120
  }
}
```

#### 智能選題算法

##### 選題策略配置
```json
{
  "selection_strategies": {
    "balanced": {
      "name": "均衡分配",
      "description": "按難度和題型均衡分配",
      "weights": {
        "difficulty": 0.4,
        "topic_coverage": 0.3,
        "recent_performance": 0.2,
        "question_quality": 0.1
      }
    },
    "adaptive": {
      "name": "適應性選題",
      "description": "根據學生歷史表現調整",
      "weights": {
        "student_weakness": 0.5,
        "difficulty": 0.3,
        "topic_coverage": 0.2
      }
    },
    "curriculum_aligned": {
      "name": "課程對齊",
      "description": "嚴格按照課程大綱選題",
      "weights": {
        "curriculum_code": 0.6,
        "difficulty": 0.3,
        "topic_coverage": 0.1
      }
    }
  }
}
```

##### 選題約束條件
```json
{
  "constraints": {
    "mandatory": {
      "min_questions_per_type": 2,
      "max_questions_per_type": 10,
      "total_time_limit": 3600,
      "min_total_points": 20,
      "max_total_points": 100
    },
    "quality": {
      "min_question_rating": 3.0,
      "exclude_flagged": true,
      "max_question_age_days": 365,
      "require_explanation": true
    },
    "diversity": {
      "max_same_source": 3,
      "min_different_topics": 5,
      "avoid_similar_content": true
    }
  }
}
```

#### 試卷生成流程

##### 1. 需求分析階段
```json
{
  "generation_request": {
    "request_id": "gen_req_001",
    "teacher_id": "teacher_001",
    "target_grade": "P5",
    "subject": "Chinese",
    "requirements": {
      "total_questions": 15,
      "total_points": 36,
      "time_limit": 60,
      "difficulty_level": "mixed",
      "focus_areas": ["詞語運用", "閱讀理解", "語法基礎"],
      "exclude_topics": ["古詩詞"],
      "special_requirements": "適合期中考試"
    },
    "created_at": "2024-01-01T10:00:00Z"
  }
}
```

##### 2. 候選題目篩選
```sql
-- 偽代碼示例
SELECT q.*, qt.type_name, AVG(r.rating) as avg_rating
FROM Question q
JOIN QuestionType qt ON q.type_id = qt.type_id
LEFT JOIN QuestionRating r ON q.question_id = r.question_id
WHERE q.status = 'active'
  AND q.difficulty IN ('beginner', 'intermediate', 'advanced')
  AND q.tags && ['詞語運用', '閱讀理解', '語法基礎']
  AND q.question_id NOT IN (SELECT question_id FROM ExcludedQuestions)
GROUP BY q.question_id
HAVING AVG(r.rating) >= 3.0
ORDER BY avg_rating DESC, q.created_at DESC
```

##### 3. 智能組卷算法
```json
{
  "algorithm_config": {
    "method": "genetic_algorithm",
    "parameters": {
      "population_size": 100,
      "generations": 50,
      "mutation_rate": 0.1,
      "crossover_rate": 0.8,
      "fitness_function": "weighted_score"
    },
    "fitness_criteria": {
      "difficulty_balance": 0.25,
      "topic_coverage": 0.25,
      "time_distribution": 0.20,
      "point_distribution": 0.20,
      "question_quality": 0.10
    }
  }
}
```

#### 試卷預覽與編輯

##### 預覽配置
```json
{
  "preview_config": {
    "view_modes": ["student_view", "teacher_view", "print_view"],
    "show_answers": false,
    "show_points": true,
    "show_time_estimates": true,
    "highlight_changes": true,
    "responsive_preview": true
  }
}
```

##### 編輯操作
```json
{
  "edit_operations": [
    {
      "operation": "replace_question",
      "old_question_id": "q_word_001",
      "new_question_id": "q_word_002",
      "reason": "難度不適合",
      "timestamp": "2024-01-01T10:30:00Z"
    },
    {
      "operation": "adjust_points",
      "question_id": "q_reading_001",
      "old_points": 8,
      "new_points": 10,
      "reason": "增加閱讀理解權重",
      "timestamp": "2024-01-01T10:35:00Z"
    },
    {
      "operation": "reorder_questions",
      "section": "二、量詞運用",
      "new_order": ["q_quant_003", "q_quant_001", "q_quant_002"],
      "reason": "按難度遞增排列",
      "timestamp": "2024-01-01T10:40:00Z"
    }
  ]
}
```

#### 試卷模板管理

##### 模板分類
```json
{
  "template_categories": {
    "exam_type": {
      "midterm": "期中考試",
      "final": "期末考試",
      "quiz": "小測驗",
      "practice": "練習卷",
      "mock": "模擬試卷"
    },
    "difficulty": {
      "basic": "基礎版",
      "standard": "標準版",
      "advanced": "進階版",
      "mixed": "混合版"
    },
    "focus": {
      "comprehensive": "綜合能力",
      "reading": "閱讀專項",
      "grammar": "語法專項",
      "vocabulary": "詞彙專項"
    }
  }
}
```

##### 模板共享機制
```json
{
  "sharing_config": {
    "visibility": "school_only",
    "permissions": {
      "view": ["teacher", "admin"],
      "edit": ["creator", "admin"],
      "copy": ["teacher"],
      "delete": ["creator", "admin"]
    },
    "approval_required": true,
    "version_control": true
  }
}
```

### 3. 學生測驗介面

#### 響應式設計詳細規格

##### 手機版設計 (< 768px)
```json
{
  "mobile_layout": {
    "container": {
      "max_width": "100%",
      "padding": "0.5rem",
      "margin": "0"
    },
    "question_card": {
      "padding": "1rem",
      "margin_bottom": "1rem",
      "border_radius": "12px",
      "box_shadow": "0 2px 4px rgba(0,0,0,0.1)"
    },
    "buttons": {
      "min_height": "44px",
      "min_width": "44px",
      "font_size": "0.875rem",
      "padding": "0.5rem 1rem",
      "touch_target": "44px"
    },
    "navigation": {
      "type": "bottom_fixed",
      "height": "60px",
      "background": "white",
      "shadow": "0 -2px 4px rgba(0,0,0,0.1)"
    },
    "typography": {
      "question_title": "1.1rem",
      "question_text": "1rem",
      "option_text": "0.9rem",
      "line_height": "1.6"
    }
  }
}
```

##### 平板版設計 (768px - 1024px)
```json
{
  "tablet_layout": {
    "container": {
      "max_width": "768px",
      "padding": "1rem",
      "margin": "0 auto"
    },
    "reading_comprehension": {
      "layout": "horizontal_split",
      "article_width": "50%",
      "questions_width": "50%",
      "gap": "1.5rem"
    },
    "question_card": {
      "padding": "1.5rem",
      "margin_bottom": "1.5rem"
    },
    "navigation": {
      "type": "top_sticky",
      "height": "70px",
      "show_progress": true,
      "show_timer": true
    },
    "sidebar": {
      "width": "200px",
      "position": "fixed",
      "show_question_list": true
    }
  }
}
```

##### 桌面版設計 (> 1024px)
```json
{
  "desktop_layout": {
    "container": {
      "max_width": "1200px",
      "padding": "2rem",
      "margin": "0 auto"
    },
    "multi_column": {
      "enabled": true,
      "columns": 2,
      "gap": "2rem",
      "balance": "auto"
    },
    "sidebar": {
      "width": "250px",
      "position": "fixed",
      "features": ["question_navigator", "timer", "progress", "bookmarks"]
    },
    "main_content": {
      "margin_left": "270px",
      "min_height": "calc(100vh - 120px)"
    }
  }
}
```

#### 互動功能詳細設計

##### 拖拽排序系統
```json
{
  "drag_drop_config": {
    "library": "SortableJS",
    "settings": {
      "animation": 150,
      "ghostClass": "sortable-ghost",
      "chosenClass": "sortable-chosen",
      "dragClass": "sortable-drag",
      "forceFallback": true,
      "fallbackTolerance": 3
    },
    "mobile_optimizations": {
      "delay": 100,
      "delayOnTouchOnly": true,
      "touchStartThreshold": 10,
      "fallbackOnBody": true
    },
    "accessibility": {
      "keyboard_support": true,
      "screen_reader": true,
      "focus_management": true,
      "aria_labels": true
    }
  }
}
```

##### 自動儲存機制
```json
{
  "auto_save": {
    "triggers": [
      "answer_change",
      "page_navigation",
      "time_interval",
      "focus_loss"
    ],
    "intervals": {
      "normal": 30000,
      "frequent": 10000,
      "critical": 5000
    },
    "storage": {
      "primary": "server",
      "fallback": "localStorage",
      "sync_strategy": "optimistic"
    },
    "conflict_resolution": {
      "strategy": "last_write_wins",
      "backup_versions": 3,
      "merge_capability": false
    }
  }
}
```

##### 進度追蹤系統
```json
{
  "progress_tracking": {
    "indicators": {
      "overall_progress": {
        "type": "percentage",
        "calculation": "answered_questions / total_questions",
        "display": "circular_progress"
      },
      "section_progress": {
        "type": "step_indicator",
        "show_section_names": true,
        "highlight_current": true
      },
      "time_progress": {
        "type": "countdown_timer",
        "warnings": [
          {"at": "75%", "type": "gentle"},
          {"at": "90%", "type": "urgent"},
          {"at": "95%", "type": "critical"}
        ]
      }
    },
    "visual_design": {
      "colors": {
        "completed": "#10B981",
        "current": "#3B82F6",
        "pending": "#E5E7EB",
        "warning": "#F59E0B",
        "critical": "#EF4444"
      },
      "animations": {
        "progress_update": "smooth_transition",
        "completion": "celebration_effect"
      }
    }
  }
}
```

##### 即時預覽功能
```json
{
  "preview_features": {
    "punctuation_preview": {
      "enabled": true,
      "update_trigger": "real_time",
      "highlight_style": {
        "color": "#2563EB",
        "font_weight": "700",
        "background": "#DBEAFE",
        "border_radius": "4px"
      }
    },
    "sentence_construction": {
      "enabled": true,
      "show_grammar_hints": true,
      "validate_syntax": true,
      "highlight_errors": true
    },
    "answer_validation": {
      "client_side": true,
      "show_format_errors": true,
      "suggest_corrections": false
    }
  }
}
```

#### 用戶體驗優化

##### 載入優化
```json
{
  "loading_optimization": {
    "lazy_loading": {
      "images": true,
      "questions": "viewport_based",
      "audio": "on_demand"
    },
    "preloading": {
      "next_question": true,
      "critical_resources": true,
      "fonts": true
    },
    "caching": {
      "static_assets": "aggressive",
      "question_data": "session_based",
      "user_answers": "persistent"
    }
  }
}
```

##### 無障礙設計
```json
{
  "accessibility": {
    "keyboard_navigation": {
      "tab_order": "logical",
      "skip_links": true,
      "focus_indicators": "high_contrast"
    },
    "screen_reader": {
      "aria_labels": "comprehensive",
      "live_regions": "progress_updates",
      "semantic_markup": "strict"
    },
    "visual_accessibility": {
      "color_contrast": "WCAG_AA",
      "font_scaling": "up_to_200%",
      "high_contrast_mode": true
    },
    "motor_accessibility": {
      "large_touch_targets": "44px_minimum",
      "drag_alternatives": "click_based",
      "timeout_extensions": "available"
    }
  }
}
```

##### 錯誤處理與恢復
```json
{
  "error_handling": {
    "network_errors": {
      "retry_strategy": "exponential_backoff",
      "offline_mode": "limited_functionality",
      "user_notification": "non_intrusive"
    },
    "data_corruption": {
      "validation": "client_and_server",
      "recovery": "backup_versions",
      "user_action": "manual_review"
    },
    "session_timeout": {
      "warning_time": 300,
      "grace_period": 60,
      "auto_save": "before_timeout"
    }
  }
}
```

### 4. 老師管理介面
#### 主要功能
- 題庫瀏覽與搜尋
- 試卷創建與編輯
- 學生成績查看
- 班級管理
- 學習報告生成

#### 權限管理
- 老師帳戶註冊/登入
- 班級權限控制
- 題庫存取權限
- 成績查看權限

### 5. 成績統計系統

#### 統計資料結構

##### 測驗結果 (TestResult)
```json
{
  "result_id": "result_001",
  "session_id": "session_001",
  "student_id": "student_001",
  "paper_id": "paper_001",
  "scores": {
    "total_score": 28,
    "max_score": 36,
    "percentage": 77.8,
    "grade": "B+",
    "percentile": 75
  },
  "time_analysis": {
    "total_time": 3240,
    "time_limit": 3600,
    "time_per_question": {
      "average": 216,
      "median": 180,
      "min": 45,
      "max": 480
    },
    "time_efficiency": 0.85
  },
  "section_breakdown": [
    {
      "section_name": "詞語選擇",
      "questions_count": 3,
      "correct_count": 2,
      "score": 4,
      "max_score": 6,
      "percentage": 66.7,
      "average_time": 120
    }
  ],
  "difficulty_analysis": {
    "beginner": {"correct": 8, "total": 10, "percentage": 80},
    "intermediate": {"correct": 6, "total": 8, "percentage": 75},
    "advanced": {"correct": 1, "total": 2, "percentage": 50}
  },
  "completed_at": "2024-01-01T11:30:00Z",
  "feedback_generated": true
}
```

##### 學生答題記錄 (StudentAnswer)
```json
{
  "answer_id": "ans_001",
  "session_id": "session_001",
  "question_id": "q_word_001",
  "student_answer": "C",
  "correct_answer": "C",
  "is_correct": true,
  "points_earned": 2,
  "max_points": 2,
  "time_spent": 95,
  "attempt_count": 1,
  "confidence_level": "high",
  "answer_metadata": {
    "changed_times": 0,
    "hesitation_time": 15,
    "review_flagged": false
  },
  "answered_at": "2024-01-01T10:15:00Z"
}
```

#### 多維度統計分析

##### 個人成績分析
```json
{
  "personal_analytics": {
    "student_id": "student_001",
    "analysis_period": "2024-01-01_to_2024-03-31",
    "overall_performance": {
      "tests_taken": 12,
      "average_score": 82.5,
      "score_trend": "improving",
      "consistency": 0.78,
      "best_score": 94,
      "worst_score": 68
    },
    "skill_breakdown": {
      "詞語運用": {
        "mastery_level": "proficient",
        "average_score": 85,
        "improvement_rate": 0.12,
        "common_errors": ["成語誤用", "近義詞混淆"]
      },
      "閱讀理解": {
        "mastery_level": "developing",
        "average_score": 78,
        "improvement_rate": 0.08,
        "common_errors": ["段意理解", "推理判斷"]
      }
    },
    "learning_patterns": {
      "optimal_test_time": "morning",
      "average_completion_time": 0.85,
      "review_frequency": "high",
      "help_seeking": "moderate"
    }
  }
}
```

##### 班級統計分析
```json
{
  "class_analytics": {
    "class_id": "class_5A",
    "teacher_id": "teacher_001",
    "analysis_period": "2024-01-01_to_2024-03-31",
    "class_overview": {
      "student_count": 28,
      "tests_administered": 8,
      "participation_rate": 0.96,
      "average_score": 79.2,
      "score_distribution": {
        "A": 6, "B": 12, "C": 8, "D": 2, "F": 0
      }
    },
    "performance_trends": {
      "monthly_averages": [75.2, 78.1, 81.3],
      "improvement_rate": 0.08,
      "struggling_students": 3,
      "excelling_students": 8
    },
    "topic_mastery": {
      "詞語運用": {"class_average": 82, "mastery_rate": 0.75},
      "閱讀理解": {"class_average": 76, "mastery_rate": 0.68},
      "標點符號": {"class_average": 84, "mastery_rate": 0.82}
    }
  }
}
```

##### 題目統計分析
```json
{
  "question_analytics": {
    "question_id": "q_word_001",
    "usage_statistics": {
      "times_used": 156,
      "total_attempts": 156,
      "correct_rate": 0.73,
      "average_time": 118,
      "difficulty_rating": 2.8
    },
    "performance_by_group": {
      "high_achievers": {"correct_rate": 0.92, "avg_time": 95},
      "average_students": {"correct_rate": 0.71, "avg_time": 125},
      "struggling_students": {"correct_rate": 0.45, "avg_time": 165}
    },
    "common_wrong_answers": [
      {"option": "A", "frequency": 0.18, "reason": "相依為命概念混淆"},
      {"option": "D", "frequency": 0.09, "reason": "相敬如賓理解錯誤"}
    ],
    "quality_metrics": {
      "discrimination_index": 0.68,
      "item_difficulty": 0.73,
      "reliability": 0.82,
      "teacher_rating": 4.2
    }
  }
}
```

#### 智能報告生成系統

##### 個人學習報告
```json
{
  "personal_report": {
    "student_info": {
      "name": "張小明",
      "class": "5A",
      "student_id": "student_001"
    },
    "executive_summary": {
      "overall_grade": "B+",
      "key_strengths": ["詞語運用", "標點符號"],
      "improvement_areas": ["閱讀理解", "句式變換"],
      "progress_trend": "穩定進步"
    },
    "detailed_analysis": {
      "skill_radar": {
        "詞語運用": 85,
        "閱讀理解": 72,
        "標點符號": 88,
        "句子排序": 79,
        "量詞運用": 81
      },
      "time_management": {
        "efficiency_score": 0.85,
        "pacing_advice": "閱讀理解題可適當加快速度",
        "time_allocation": "建議分配更多時間給弱項練習"
      }
    },
    "personalized_recommendations": [
      {
        "area": "閱讀理解",
        "priority": "high",
        "specific_advice": "多練習段意歸納題型",
        "recommended_resources": ["閱讀理解專項練習", "段落分析技巧"]
      }
    ]
  }
}
```

##### 班級教學報告
```json
{
  "class_teaching_report": {
    "class_info": {
      "class_name": "5A班",
      "teacher": "李老師",
      "period": "2024年第一季"
    },
    "teaching_effectiveness": {
      "overall_improvement": 0.12,
      "topic_mastery_rates": {
        "詞語運用": 0.82,
        "閱讀理解": 0.68,
        "標點符號": 0.85
      },
      "differentiation_success": 0.75
    },
    "student_grouping": {
      "advanced_group": {
        "count": 8,
        "characteristics": "掌握基礎，需要挑戰性題目",
        "recommendations": "增加高階思維題型"
      },
      "standard_group": {
        "count": 16,
        "characteristics": "基礎穩固，需要鞏固練習",
        "recommendations": "重點練習應用題型"
      },
      "support_group": {
        "count": 4,
        "characteristics": "基礎薄弱，需要額外支援",
        "recommendations": "加強基礎概念教學"
      }
    },
    "curriculum_alignment": {
      "coverage_rate": 0.92,
      "pacing_assessment": "適中",
      "adjustment_suggestions": ["增加閱讀理解練習時間"]
    }
  }
}
```

#### 視覺化圖表設計

##### 圖表類型配置
```json
{
  "chart_configurations": {
    "progress_line_chart": {
      "type": "line",
      "data_points": "monthly_scores",
      "y_axis": "score_percentage",
      "x_axis": "time_period",
      "colors": ["#3B82F6", "#10B981"],
      "animations": true
    },
    "skill_radar_chart": {
      "type": "radar",
      "dimensions": ["詞語運用", "閱讀理解", "標點符號", "句子排序", "量詞運用"],
      "scale": [0, 100],
      "fill_opacity": 0.2,
      "stroke_width": 2
    },
    "score_distribution": {
      "type": "histogram",
      "bins": ["0-59", "60-69", "70-79", "80-89", "90-100"],
      "colors": ["#EF4444", "#F59E0B", "#10B981", "#3B82F6", "#8B5CF6"]
    },
    "time_analysis": {
      "type": "bar",
      "categories": "question_types",
      "metrics": ["average_time", "optimal_time"],
      "comparison": true
    }
  }
}
```

#### 報告導出功能

##### PDF報告配置
```json
{
  "pdf_export": {
    "templates": {
      "student_report": {
        "layout": "A4_portrait",
        "sections": ["header", "summary", "charts", "recommendations", "footer"],
        "branding": true,
        "color_scheme": "professional"
      },
      "class_report": {
        "layout": "A4_landscape",
        "sections": ["overview", "statistics", "student_list", "insights"],
        "charts_per_page": 4,
        "detailed_tables": true
      }
    },
    "customization": {
      "school_logo": true,
      "teacher_comments": true,
      "parent_signature_space": true,
      "multilingual": ["繁體中文", "英文"]
    }
  }
}
```

## 開發階段規劃

### 第一階段：基礎架構 (4週)
- [ ] 專案環境搭建
- [ ] 基礎架構設計
- [ ] 資料庫設計
- [ ] 用戶認證系統
- [ ] 基礎API開發

### 第二階段：題型模板開發 (6週)
- [ ] 現有6種題型模板
- [ ] 響應式設計實現
- [ ] 拖拽功能整合
- [ ] 評分系統開發
- [ ] 題庫JSON結構設計

### 第三階段：新題型開發 (4週)
- [ ] 修辭手法題型
- [ ] 句式變換題型
- [ ] 改寫句子題型
- [ ] 字形辨識題型
- [ ] 詞語辨析進階題型
- [ ] 段意理解題型

### 第四階段：管理系統 (4週)
- [ ] 老師管理介面
- [ ] 試卷生成器
- [ ] 題庫管理系統
- [ ] 成績統計功能
- [ ] 報告生成功能

### 第五階段：測試與優化 (3週)
- [ ] 功能測試
- [ ] 響應式測試
- [ ] 性能優化
- [ ] 用戶體驗優化
- [ ] 安全性測試

### 第六階段：部署與上線 (1週)
- [ ] 雲端部署
- [ ] 域名配置
- [ ] SSL證書
- [ ] 監控系統
- [ ] 備份機制

## 技術考慮

### 響應式設計重點
- Mobile-first 設計原則
- 觸控友好的界面元素
- 適應不同螢幕尺寸
- 優化載入速度
- 離線功能考慮

### 性能優化
- 圖片壓縮與懶載入
- 代碼分割與按需載入
- CDN加速
- 快取策略
- 資料庫查詢優化

### 安全性考慮
- 用戶資料加密
- API安全驗證
- XSS防護
- CSRF防護
- 資料備份機制

## 預期挑戰與解決方案

### 挑戰1：複雜的拖拽功能在移動端的實現
**解決方案**: 使用專門的移動端拖拽庫，提供觸控優化

### 挑戰2：大量題目的載入性能
**解決方案**: 實現分頁載入和虛擬滾動

### 挑戰3：不同設備的一致性體驗
**解決方案**: 建立完整的設計系統和測試流程

### 挑戰4：題庫的擴展性和維護性
**解決方案**: 設計靈活的JSON結構和版本控制系統

## 成功指標

### 技術指標
- 頁面載入時間 < 3秒
- 移動端響應時間 < 1秒
- 系統可用性 > 99%
- 支援同時在線用戶 > 1000人

### 用戶體驗指標
- 用戶完成率 > 85%
- 用戶滿意度 > 4.0/5.0
- 平均使用時長 > 20分鐘
- 重複使用率 > 60%

### 教育效果指標
- 學生成績提升幅度
- 老師使用頻率
- 題庫使用覆蓋率
- 錯題改善率

## 後續發展規劃

### 短期擴展 (6個月內)
- 支援小四、小六年級
- 增加更多題型
- 開發家長查看功能
- 增加遊戲化元素

### 中期擴展 (1年內)
- 支援其他科目 (數學、英文)
- 開發AI智能推薦
- 增加協作學習功能
- 開發移動APP

### 長期願景 (2年內)
- 建立完整的學習生態系統
- 支援全港小學課程
- 開發教師培訓平台
- 拓展至其他地區市場
