# AI 語文練習系統 - Netlify 部署版

這是一個支援 AI 智能批改的中文句子改寫和擴寫練習系統，專為 Netlify 部署優化。

## 🚀 快速部署

### 1. 準備 Google Gemini API 金鑰

1. 訪問 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登入你的 Google 帳戶
3. 點擊「Create API Key」
4. 複製生成的 API 金鑰

### 2. 部署到 Netlify

#### 方法 A: 通過 Git 部署（推薦）

1. 將此專案推送到 GitHub 倉庫
2. 登入 [Netlify](https://netlify.com)
3. 點擊「New site from Git」
4. 選擇你的 GitHub 倉庫
5. 設置建構配置：
   - Build command: `echo 'No build required'`
   - Publish directory: `.`
6. 點擊「Deploy site」

#### 方法 B: 拖拽部署

1. 將整個 `Reference2` 資料夾壓縮成 ZIP 檔案
2. 登入 [Netlify](https://netlify.com)
3. 直接將 ZIP 檔案拖拽到 Netlify 控制台

### 3. 設置環境變數

1. 在 Netlify 控制台中，進入你的網站設置
2. 點擊「Environment variables」
3. 添加新的環境變數：
   - **Key**: `GEMINI_API_KEY`
   - **Value**: 你的 Google Gemini API 金鑰
4. 點擊「Save」

### 4. 重新部署

設置環境變數後，點擊「Trigger deploy」重新部署網站。

## 🧪 測試 AI 功能

部署完成後：

1. 訪問你的 Netlify 網站 URL
2. 選擇「改寫句子」或「擴寫句子」
3. 輸入你的答案
4. 點擊「AI 批改」按鈕
5. 等待 AI 回饋（通常需要 5-10 秒）

## 📁 檔案結構

```
Reference2/
├── index.html                    # 主要網頁檔案
├── netlify.toml                  # Netlify 配置檔案
├── netlify/
│   └── functions/
│       └── ai-grading.js         # AI 批改 Netlify Function
└── README.md                     # 說明文檔
```

## 🔧 技術架構

- **前端**: 純 HTML/CSS/JavaScript
- **AI 服務**: Google Gemini API
- **部署**: Netlify 靜態託管 + Functions
- **API 代理**: Netlify Functions（隱藏 API 金鑰）

## 🐛 故障排除

### AI 批改不工作

1. **檢查環境變數**:
   - 確保 `GEMINI_API_KEY` 已正確設置
   - API 金鑰沒有多餘的空格

2. **檢查 API 金鑰**:
   - 確保 API 金鑰有效且未過期
   - 確保 Gemini API 已啟用

3. **檢查網路連接**:
   - 確保網站可以訪問外部 API
   - 檢查瀏覽器控制台是否有錯誤訊息

### 查看日誌

1. 在 Netlify 控制台中點擊「Functions」
2. 點擊「ai-grading」函數
3. 查看「Function logs」了解詳細錯誤訊息

## 💰 成本估算

- **Netlify**: 免費版支援 125,000 次函數調用/月
- **Google Gemini API**: 
  - 免費額度: 每分鐘 15 次請求
  - 付費版: 約 $0.0001 USD/次請求

## 🔒 安全性

- API 金鑰安全存儲在 Netlify 環境變數中
- 前端無法直接訪問 API 金鑰
- 支援 CORS 和基本的安全標頭

## 📝 自訂修改

### 修改 AI 提示

編輯 `netlify/functions/ai-grading.js` 中的 `prompt` 變數來自訂 AI 批改的行為。

### 添加新題型

在 `index.html` 中的 `questions` 物件中添加新的題目。

### 修改樣式

直接編輯 `index.html` 中的 CSS 樣式。

## 📞 支援

如果遇到問題，請檢查：
1. Netlify Function 日誌
2. 瀏覽器開發者工具控制台
3. 網路請求狀態

## 🎯 下一步

- 添加更多題型
- 實現用戶進度追蹤
- 添加離線功能
- 優化 AI 回饋品質
