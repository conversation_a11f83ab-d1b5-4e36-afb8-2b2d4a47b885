# 香港小五中文測驗系統 - 純前端開發計劃

## 專案概述

### 目標與定位
- **主要目標**: 協助香港小五學生準備中文科呈分試
- **目標用戶**: 香港小五學生（主要）、中文科老師（次要）
- **核心價值**: 提供互動式中文練習，結合 AI 智能批改功能

### 設備支援策略
- **主要設備**: 平板電腦、手機（Mobile-first 設計）
- **次要設備**: 桌面電腦
- **響應式要求**: 優先考慮觸控操作和移動端體驗

## 技術架構

### 整體架構（純前端方案）
```
香港小五中文測驗系統
├── 前端單頁應用 (SPA)
│   ├── 學生測驗介面
│   ├── 老師管理介面
│   └── AI 批改功能
├── 無伺服器函數 (Netlify Functions)
│   └── AI 批改代理服務（僅此一項需要伺服器端）
├── 靜態資源
│   ├── 題庫 JSON 檔案（存放在 /public/data/）
│   ├── 圖片和媒體檔案
│   └── 應用程式資源
├── 客戶端存儲
│   ├── LocalStorage (用戶設置)
│   ├── IndexedDB (測驗記錄)
│   └── 記憶體快取 (運行時資料)
└── 部署平台
    └── Netlify (靜態託管 + Functions)
```

### 純前端架構的優勢與限制

#### ✅ **優勢**
- **零伺服器維護成本**: 除 AI API 外無需後端服務
- **極快的載入速度**: 靜態資源通過 CDN 分發
- **完全離線可用**: 支援 Service Worker 離線功能
- **無限擴展性**: 靜態網站可承受大量並發用戶
- **資料隱私保護**: 用戶資料完全存在本地
- **簡單的部署流程**: Git push 即可自動部署

#### ❌ **限制**
- **無法跨設備同步**: 資料僅存在單一設備
- **無即時協作功能**: 無法實現多用戶即時互動
- **有限的資料分析**: 無法收集詳細的用戶行為資料
- **題庫更新需重新部署**: 無法動態更新題目內容
- **無用戶認證系統**: 無法區分不同用戶身份
- **AI 功能依賴外部服務**: 需要穩定的網路連接

### 技術棧選擇
- **前端框架**: React.js 或 Vue.js
- **建構工具**: Vite（快速開發和建構）
- **樣式框架**: Tailwind CSS
- **拖拽功能**: SortableJS
- **圖表庫**: Chart.js 或 D3.js
- **AI 服務**: Google Gemini API（通過 Netlify Functions）
- **部署平台**: Netlify
- **版本控制**: Git + GitHub

## 功能模組設計

### 1. 題型系統

#### 1.1 現有題型
**詞語選擇**
- 成語運用選擇題
- 詞語辨析題
- 分值: 每題 2 分
- 互動方式: 單選按鈕

**量詞運用**
- 物量詞/動量詞選擇
- 量詞類型判斷
- 分值: 每個答案 1 分
- 互動方式: 單選按鈕

**句子排序**
- 多句子邏輯排列
- 拖拽互動功能
- 分值: 4 分
- 互動方式: 點擊選擇 + 拖拽排序

**重組句子**
- 詞語拖拽組合
- 語法結構訓練
- 分值: 4 分
- 互動方式: 點擊選擇 + 拖拽排序

**標點符號**
- 即時預覽功能
- 標點符號選擇
- 分值: 6 分
- 互動方式: 單選按鈕 + 即時預覽

**閱讀理解**
- 響應式文章+問題佈局
- 多種問答形式
- 分值: 8 分
- 互動方式: 選擇題 + 文字輸入

#### 1.2 AI 批改題型
**句子改寫**
- 句式變換（明喻改暗喻、陳述句改反問句等）
- 使用提示引導學生
- AI 智能批改和回饋
- 互動方式: 文字輸入 + AI 分析

**句子擴寫**
- 增加修飾語、情境描述
- 創意寫作訓練
- AI 智能批改和回饋
- 互動方式: 文字輸入 + AI 分析

#### 1.3 需要補充的題型
**修辭手法辨析**
- 比喻、擬人、排比等識別
- 修辭手法運用練習
- 分值: 4 分

**字形辨識**
- 形近字辨別
- 同音字選擇
- 錯別字糾正
- 分值: 4 分

**詞語辨析進階**
- 近義詞選擇
- 反義詞配對
- 詞語配搭
- 分值: 4 分

### 2. AI 批改系統

#### 2.1 技術實現架構
```
前端 React/Vue 應用
    ↓ HTTPS POST 請求
Netlify Functions (ai-grading.js)
    ↓ API 調用
Google Gemini API
    ↓ JSON 回應
結構化批改回饋
```

#### 2.2 AI 批改流程
1. **用戶輸入**: 學生完成句子改寫/擴寫
2. **資料準備**: 整理原句、提示、學生答案
3. **API 調用**: 通過 Netlify Function 調用 Gemini API
4. **智能分析**: AI 分析答案品質和改進建議
5. **結構化回饋**: 返回評價、優點、改進建議
6. **前端展示**: 以卡片形式展示批改結果

#### 2.3 批改回饋結構
```json
{
  "evaluation": "總體評價（正面鼓勵）",
  "strengths": "具體優點分析",
  "improvements": "改進建議或鼓勵"
}
```

#### 2.4 Netlify Function 設計要點
- API 金鑰安全存儲在環境變數
- 支援可配置的使用限制（預設無限制）
- 錯誤處理和超時保護
- CORS 支援和安全驗證

### 3. 資料存儲系統（純前端方案）

#### 3.1 存儲架構說明
**重要**: 本系統採用純前端架構，**沒有傳統意義上的資料庫**

**資料存儲方式**:
- **靜態 JSON 檔案**: 題庫內容（存放在 `/public/data/` 資料夾）
- **瀏覽器本地存儲**: 用戶資料和測驗記錄
- **記憶體存儲**: 運行時的應用程式狀態

#### 3.2 題庫資料結構（靜態 JSON 檔案）
```
/public/data/
├── question-bank-index.json     # 題庫索引
├── questions-word-choice.json   # 詞語選擇題
├── questions-reading.json       # 閱讀理解題
├── questions-ai-grading.json    # AI 批改題
└── questions-grammar.json       # 語法相關題型
```

**主索引檔案格式**:
```json
{
  "questionBank": {
    "metadata": {
      "version": "1.0.0",
      "lastUpdated": "2024-01-01",
      "totalQuestions": 150
    },
    "categories": [
      {
        "id": "word_choice",
        "name": "詞語選擇",
        "description": "成語運用、詞語辨析",
        "dataFile": "questions-word-choice.json",
        "questionCount": 25
      }
    ]
  }
}
```

#### 3.2 題目資料格式
**標準選擇題格式**
```json
{
  "question_id": "q_word_001",
  "type": "word_choice",
  "difficulty": "intermediate",
  "points": 2,
  "question_text": "題目內容",
  "options": [
    {"id": "A", "text": "選項A", "correct": false},
    {"id": "B", "text": "選項B", "correct": true}
  ],
  "explanation": "答案解析",
  "tags": ["成語運用", "家庭關係"]
}
```

**AI 批改題格式**
```json
{
  "question_id": "q_rewrite_001",
  "type": "sentence_rewrite",
  "difficulty": "intermediate", 
  "points": 4,
  "original_sentence": "原句內容",
  "hint": "改寫提示",
  "model_answer": "參考答案",
  "ai_prompt_template": "AI 批改提示模板"
}
```

#### 3.4 客戶端存儲策略
**LocalStorage 用途**（簡單鍵值對存儲）
- 用戶偏好設置（主題、字體大小等）
- 當前測驗進度（防止意外關閉瀏覽器）
- AI 批改配額記錄（如有限制）
- 最近使用的題型和難度設定

**IndexedDB 用途**（複雜資料結構存儲）
- 個人測驗歷史記錄
- 錯題收集和復習記錄
- 離線題庫快取（提升載入速度）
- 學習進度和統計資料

**記憶體存儲**（運行時狀態）
- 當前測驗會話資料
- AI 批改回應快取
- 題目載入快取
- 用戶介面狀態

#### 3.5 資料同步限制
**純前端限制**:
- ❌ 無法跨設備同步資料
- ❌ 無法即時分享測驗結果
- ❌ 無法實現多用戶協作
- ✅ 完全離線可用
- ✅ 無伺服器維護成本
- ✅ 資料隱私保護

### 4. 響應式設計規格

#### 4.1 斷點設計
- **手機**: < 768px
- **平板**: 768px - 1024px  
- **桌面**: > 1024px

#### 4.2 手機版設計要點
- 單欄佈局，垂直滾動
- 大觸控按鈕（最小 44px）
- 底部固定導航
- 簡化的題目展示
- 拖拽功能觸控優化

#### 4.3 平板版設計要點
- 閱讀理解採用左右分欄
- 側邊導航顯示進度
- 適中的按鈕和字體大小
- 支援橫豎屏切換

#### 4.4 桌面版設計要點
- 多欄佈局，充分利用螢幕空間
- 側邊欄顯示題目導航
- 完整功能展示
- 鍵盤快捷鍵支援

### 5. 用戶介面系統

#### 5.1 學生測驗介面
**主要功能**
- 分頁式題目展示
- 進度追蹤和時間提醒
- 自動儲存答案
- 即時互動回饋
- AI 批改結果展示

**導航系統**
- 上一題/下一題按鈕
- 進度指示器
- 題目跳轉功能
- 完成提交按鈕

#### 5.2 老師管理介面（純前端實現）
**題庫瀏覽功能**
- 按題型分類瀏覽（從靜態 JSON 載入）
- 前端搜尋和篩選功能
- 題目預覽和詳細資訊
- 難度標示和標籤顯示

**試卷生成功能**
- 拖拽式題目選擇介面
- 自動分值計算（前端邏輯）
- 即時試卷預覽
- 匯出為 PDF 或列印格式
- 生成分享連結（URL 參數方式）

**限制說明**:
- ❌ 無法永久保存自訂試卷到伺服器
- ❌ 無法跨設備同步試卷
- ✅ 可匯出試卷檔案到本地
- ✅ 可通過 URL 分享試卷配置

#### 5.3 成績統計介面（本地資料）
**個人成績分析**
- 測驗歷史記錄（從 IndexedDB 讀取）
- 分數趨勢圖表（Chart.js 實現）
- 錯題統計和分析
- 強弱項識別和建議
- 學習時間統計

**資料匯出功能**
- 匯出個人成績報告（PDF）
- 匯出錯題集（JSON/CSV）
- 匯出學習進度圖表

**純前端限制**:
- ❌ 無法實現班級統計功能
- ❌ 無法比較不同學生成績
- ❌ 無法生成班級報告
- ✅ 完整的個人學習分析
- ✅ 詳細的進步追蹤

## 開發階段規劃

### 第一階段：基礎架構（3週）
**目標**: 建立專案基礎和核心架構
- [ ] 專案初始化和環境配置
- [ ] Netlify 部署環境設置
- [ ] 基礎 UI 組件庫建立
- [ ] 響應式佈局框架
- [ ] 題庫 JSON 結構設計

### 第二階段：核心題型實現（4週）
**目標**: 實現 Reference 中的 6 種基礎題型
- [ ] 詞語選擇題組件
- [ ] 量詞運用題組件
- [ ] 句子排序題組件（含拖拽功能）
- [ ] 重組句子題組件
- [ ] 標點符號題組件（含即時預覽）
- [ ] 閱讀理解題組件（響應式佈局）

### 第三階段：AI 批改功能（3週）
**目標**: 整合 AI 批改系統
- [ ] Netlify Functions 設置
- [ ] Gemini API 整合
- [ ] 句子改寫題型實現
- [ ] 句子擴寫題型實現
- [ ] AI 回饋介面設計
- [ ] 錯誤處理和降級方案

### 第四階段：補充題型（3週）
**目標**: 實現額外的中文科題型
- [ ] 修辭手法辨析題型
- [ ] 字形辨識題型
- [ ] 詞語辨析進階題型
- [ ] 題型整合測試

### 第五階段：管理功能（2週）
**目標**: 實現純前端管理和統計功能
- [ ] 題庫瀏覽介面（靜態 JSON 載入）
- [ ] 試卷生成器（前端邏輯）
- [ ] 個人成績統計功能（IndexedDB 資料）
- [ ] 資料匯出功能（PDF/CSV 生成）
- [ ] URL 分享機制實現

### 第六階段：優化和部署（2週）
**目標**: 性能優化和正式部署
- [ ] 性能優化和程式碼分割
- [ ] 跨瀏覽器相容性測試
- [ ] 無障礙功能完善
- [ ] 正式部署和監控設置

## 技術實現要點（純前端特化）

### 1. 拖拽功能實現
- **庫選擇**: SortableJS（輕量級，支援觸控）
- **移動端優化**: 觸控延遲、手勢識別
- **無障礙支援**: 鍵盤導航、螢幕閱讀器支援
- **視覺回饋**: CSS 動畫、拖拽預覽效果
- **狀態管理**: 前端狀態同步，LocalStorage 備份

### 2. 靜態資源管理
- **題庫載入**: 按需載入 JSON 檔案，避免一次載入全部
- **圖片優化**: WebP 格式，多解析度適配
- **懶載入**: Intersection Observer API 實現
- **快取策略**: 瀏覽器快取 + Service Worker 快取

### 3. 離線功能支援
- **Service Worker**: 快取關鍵資源和 API 回應
- **離線檢測**: Navigator.onLine API 監控網路狀態
- **降級方案**: 網路不可用時的功能限制提示
- **資料同步**: 網路恢復時的資料上傳機制

### 4. 前端性能優化
- **程式碼分割**: 按路由和功能模組分割
- **懶載入**: 題型組件按需載入
- **虛擬滾動**: 大量題目列表的性能優化
- **記憶體管理**: 及時清理不需要的資料和事件監聽器

### 5. 資料管理策略
- **JSON 檔案結構**: 模組化分割，便於維護和載入
- **前端搜尋**: 使用 Fuse.js 實現模糊搜尋
- **資料驗證**: 前端驗證 JSON 資料完整性
- **版本控制**: 資料版本檢查和更新提示

## 部署和維護

### Netlify 部署配置
- 自動化 CI/CD 流程
- 環境變數管理
- 自訂域名配置
- HTTPS 和安全標頭

### 監控和分析
- 使用 Netlify Analytics
- 錯誤追蹤和日誌
- 性能監控
- 用戶行為分析

### 維護策略（純前端限制）
- **題庫更新**: 修改 JSON 檔案後重新部署
- **版本控制**: 通過 Git 管理所有變更
- **AI 模型**: 僅能更新 Prompt 模板，無法訓練自訂模型
- **用戶回饋**: 通過表單服務（如 Netlify Forms）收集
- **監控限制**: 僅能監控前端錯誤和性能，無法追蹤用戶行為細節

## 成功指標

### 技術指標
- 頁面載入時間 < 3 秒
- 移動端響應時間 < 1 秒
- AI 批改回應時間 < 10 秒
- 跨瀏覽器相容性 > 95%

### 用戶體驗指標
- 測驗完成率 > 85%
- AI 批改滿意度 > 4.0/5.0
- 移動端使用比例 > 70%
- 用戶重複使用率 > 60%

### 教育效果指標
- 學生答題準確率提升
- AI 批改回饋有效性
- 老師使用頻率
- 題庫覆蓋度

## 風險管理

### 技術風險
- **AI API 不穩定**: 實現重試機制和錯誤處理
- **移動端相容性**: 充分的設備測試
- **性能問題**: 程式碼優化和資源壓縮

### 成本風險
- **AI API 費用**: 實現使用監控和限制機制
- **Netlify 超額**: 監控使用量和優化資源

### 用戶體驗風險
- **學習曲線**: 提供清晰的使用指南
- **技術門檻**: 簡化操作流程

## 未來擴展規劃

### 短期擴展（6個月內）
- 支援小四、小六年級
- 增加更多 AI 批改題型
- 開發離線模式

### 中期擴展（1年內）
- 支援其他科目（數學、英文）
- 開發教師培訓模組
- 增加遊戲化元素

### 長期願景（2年內）
- 建立完整的學習生態系統
- 支援全港小學課程
- 拓展至其他地區市場
