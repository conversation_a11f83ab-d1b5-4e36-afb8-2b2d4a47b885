<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <!-- viewport meta tag 是響應式設計的關鍵，確保網頁在不同設備上以正確的比例顯示 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 語文練習：改寫與擴寫句子</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', 'Noto Sans TC', sans-serif;
        }
        .tab-active {
            border-color: #3b82f6;
            color: #3b82f6;
            background-color: #eff6ff;
        }
        .feedback-card {
            background-color: #f9fafb;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.5rem;
            margin-top: 1.5rem;
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .loader {
            border: 4px solid #f3f3f3;
            border-radius: 50%;
            border-top: 4px solid #3498db;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<!-- body 使用 flex 佈局，讓內容在各種螢幕尺寸下都能垂直和水平置中 -->
<body class="bg-gray-50 text-gray-800 flex items-center justify-center min-h-screen p-4">

    <!-- w-full 確保在小螢幕上佔滿寬度，max-w-3xl 限制在大螢幕上的最大寬度，避免內容過度拉伸 -->
    <!-- p-6 md:p-8 針對不同螢幕尺寸調整內邊距，手機上較小，平板和桌機上較大 -->
    <div class="w-full max-w-3xl bg-white rounded-2xl shadow-lg p-6 md:p-8">
        
        <!-- 標題 -->
        <header class="text-center mb-6">
            <!-- text-3xl md:text-4xl 響應式字體大小，手機上為 3xl，中等螢幕(平板)以上為 4xl -->
            <h1 class="text-3xl md:text-4xl font-bold text-gray-900">AI 語文練習坊</h1>
            <p class="text-gray-500 mt-2">改寫與擴寫句子</p>
        </header>

        <!-- 頁籤 -->
        <div class="flex border-b border-gray-200 mb-6">
            <button id="tab-rewrite" class="flex-1 py-3 px-2 text-center font-semibold text-gray-600 border-b-2 border-transparent hover:bg-gray-100 transition-colors duration-200 rounded-t-lg">改寫句子</button>
            <button id="tab-expand" class="flex-1 py-3 px-2 text-center font-semibold text-gray-600 border-b-2 border-transparent hover:bg-gray-100 transition-colors duration-200 rounded-t-lg">擴寫句子</button>
        </div>

        <!-- 練習區 -->
        <main id="practice-area">
            <div id="question-card" class="bg-blue-50 p-5 rounded-xl border border-blue-200">
                <h2 id="question-type" class="text-lg font-semibold text-blue-800 mb-2"></h2>
                <p class="text-gray-700 mb-2"><strong>原句：</strong><span id="original-sentence"></span></p>
                <p id="hint-container" class="text-sm text-blue-600"><strong>提示：</strong><span id="hint"></span></p>
            </div>

            <div class="mt-4">
                <textarea id="user-answer" rows="5" class="w-full p-4 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-shadow duration-200" placeholder="請在此輸入您的答案..."></textarea>
            </div>

            <!-- 按鈕容器：手機上垂直排列(flex-col)，平板以上水平排列(sm:flex-row) -->
            <div class="mt-6 flex flex-col sm:flex-row gap-4">
                <button id="grade-btn" class="w-full sm:w-1/2 bg-blue-600 text-white font-bold py-3 px-6 rounded-xl hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all duration-300 transform hover:scale-105">
                    AI 批改
                </button>
                <button id="next-btn" class="w-full sm:w-1/2 bg-gray-200 text-gray-800 font-bold py-3 px-6 rounded-xl hover:bg-gray-300 focus:outline-none focus:ring-4 focus:ring-gray-300 transition-all duration-300">
                    下一題
                </button>
            </div>
        </main>
        
        <!-- AI 回饋區 -->
        <div id="feedback-section" class="mt-6">
             <!-- 載入動畫會顯示在這裡 -->
             <!-- 回饋卡片會顯示在這裡 -->
        </div>

    </div>

    <script>
        // --- 資料庫 ---
        const questions = {
            rewrite: [
                { original: "他交遊廣闊，沒有一個稱得上是知心朋友。", hint: "使用「雖然...但...」的句式。", modelAnswer: "他雖然交遊廣闊，但沒有一個稱得上是知心朋友。" },
                { original: "他非常狡猾，像一隻狐狸。", hint: "將「像」字句（明喻）改為「是」字句（暗喻）。", modelAnswer: "他非常狡猾，是一隻狐狸。" },
                { original: "我怎麼又回到這裏了，我走錯路了。", hint: "將陳述句改為反問句。", modelAnswer: "我怎麼又回到這裏了，難道我走錯路了？" },
                { original: "我視這兒為自己的家，絕不會離開。", hint: "使用「不但...而且...」的句式。", modelAnswer: "我不但視這兒為自己的家，而且絕不會離開。" },
                { original: "在一片歡呼聲中，健兒向着終點奔過去。", hint: "將「健兒」比喻成更具動感的東西。", modelAnswer: "在一片歡呼聲中，健兒變成駿馬向着終點奔過去。" },
                { original: "姐姐的臉紅通通的，像一個熟透的紅蘋果。", hint: "將明喻句改為暗喻句。", modelAnswer: "姐姐的臉紅通通的，是一個熟透的紅蘋果。" }
            ],
            expand: [
                { original: "小嬰兒在睡牀熟睡了。", hint: "小嬰兒變成了甚麼？", modelAnswer: "小嬰兒變成了溫馴的綿羊，在睡牀熟睡了。" },
                { original: "早上，我被小鳥的叫聲喚醒。", hint: "小鳥的叫聲怎麼樣？", modelAnswer: "早上，我被小鳥「吱吱」的叫聲喚醒。" },
                { original: "我和她漸漸成了朋友。", hint: "甚麼事情後？我和她成了怎樣的朋友？", modelAnswer: "經過那次一起完成專題研習後，我和她漸漸成了無所不談的朋友。" },
                { original: "排隊的人不斷湧向場內。", hint: "在甚麼情況下？排隊的人像甚麼？", modelAnswer: "文化博物館舉行特別展覽，排隊的人像潮水不斷湧向場內。" },
                { original: "這裏景色優美。", hint: "清晨時景色如何？黃昏時景色如何？給人甚麼感受？", modelAnswer: "這裏景色優美，清晨時太陽緩緩升起，黃昏時太陽徐徐西下，使人流連忘返。" },
                { original: "同學生了我的氣。", hint: "由於甚麼原因，同學生了我的氣？", modelAnswer: "由於我失約，同學生了我的氣。" }
            ]
        };

        // --- DOM 元素 ---
        const tabRewrite = document.getElementById('tab-rewrite');
        const tabExpand = document.getElementById('tab-expand');
        const questionTypeEl = document.getElementById('question-type');
        const originalSentenceEl = document.getElementById('original-sentence');
        const hintContainerEl = document.getElementById('hint-container');
        const hintEl = document.getElementById('hint');
        const userAnswerEl = document.getElementById('user-answer');
        const gradeBtn = document.getElementById('grade-btn');
        const nextBtn = document.getElementById('next-btn');
        const feedbackSection = document.getElementById('feedback-section');

        // --- 狀態管理 ---
        let currentType = 'rewrite';
        let currentQuestion = {};
        let usedIndices = { rewrite: [], expand: [] };

        // --- 函數 ---

        // 顯示新題目
        function displayNewQuestion() {
            const questionSet = questions[currentType];
            if (usedIndices[currentType].length === questionSet.length) {
                usedIndices[currentType] = []; // 如果所有題目都問過了，就重置
            }

            let randomIndex;
            do {
                randomIndex = Math.floor(Math.random() * questionSet.length);
            } while (usedIndices[currentType].includes(randomIndex));

            usedIndices[currentType].push(randomIndex);
            currentQuestion = questionSet[randomIndex];

            questionTypeEl.textContent = currentType === 'rewrite' ? '改寫句子' : '擴寫句子';
            originalSentenceEl.textContent = currentQuestion.original;
            
            if (currentQuestion.hint) {
                hintEl.textContent = currentQuestion.hint;
                hintContainerEl.style.display = 'block';
            } else {
                hintContainerEl.style.display = 'none';
            }

            userAnswerEl.value = '';
            feedbackSection.innerHTML = '';
            gradeBtn.disabled = false;
            gradeBtn.textContent = 'AI 批改';
        }

        // 切換頁籤
        function switchTab(type) {
            currentType = type;
            if (type === 'rewrite') {
                tabRewrite.classList.add('tab-active');
                tabExpand.classList.remove('tab-active');
            } else {
                tabExpand.classList.add('tab-active');
                tabRewrite.classList.remove('tab-active');
            }
            displayNewQuestion();
        }

        // 顯示載入動畫
        function showLoader() {
            feedbackSection.innerHTML = `
                <div class="flex flex-col items-center justify-center p-6">
                    <div class="loader"></div>
                    <p class="mt-4 text-gray-600">AI 老師正在批改中，請稍候...</p>
                </div>
            `;
        }

        // 顯示 AI 回饋
        function showFeedback(feedback) {
            feedbackSection.innerHTML = `
                <div class="feedback-card">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">AI 老師回饋</h3>
                    <div class="space-y-4">
                        <div>
                            <h4 class="font-semibold text-gray-700">⭐ 總體評價</h4>
                            <p class="text-gray-600 mt-1">${feedback.evaluation || '未能分析評價。'}</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700">👍 優點</h4>
                            <p class="text-gray-600 mt-1">${feedback.strengths || '未能分析優點。'}</p>
                        </div>
                        <div>
                            <h4 class="font-semibold text-gray-700">💡 可改進之處</h4>
                            <p class="text-gray-600 mt-1">${feedback.improvements || '未能分析可改進之處。'}</p>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // 處理 API 錯誤
        function handleApiError(error) {
            console.error("API Error:", error);
            feedbackSection.innerHTML = `
                 <div class="feedback-card bg-red-50 border-red-200">
                    <h3 class="text-xl font-bold text-red-800 mb-2">批改出錯</h3>
                    <p class="text-red-700">抱歉，AI 老師暫時無法批改。請稍後再試。</p>
                </div>
            `;
        }

        // 呼叫 Netlify Function 進行 AI 批改
        async function getAIFeedback(userAnswer) {
            const exerciseType = currentType === 'rewrite' ? '改寫句子' : '擴寫句子';

            try {
                console.log('Calling AI grading function...');

                const response = await fetch('/.netlify/functions/ai-grading', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        userAnswer: userAnswer,
                        originalSentence: currentQuestion.original,
                        hint: currentQuestion.hint,
                        modelAnswer: currentQuestion.modelAnswer,
                        exerciseType: exerciseType
                    })
                });

                console.log('Response status:', response.status);

                if (!response.ok) {
                    const errorData = await response.json();
                    console.error('API Error:', errorData);
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }

                const result = await response.json();
                console.log('AI feedback received:', result);

                // 檢查是否有錯誤但包含 fallback
                if (result.error && result.fallback) {
                    console.warn('Using fallback response:', result.fallback);
                    return result.fallback;
                }

                // 檢查回應格式
                if (result.evaluation && result.strengths && result.improvements) {
                    return result;
                } else {
                    console.warn('Invalid response format, using default');
                    return {
                        evaluation: "你的答案不錯！",
                        strengths: "句子表達清楚。",
                        improvements: "繼續努力練習！"
                    };
                }

            } catch (error) {
                console.error('AI grading failed:', error);

                // 返回預設回饋而不是拋出錯誤
                return {
                    evaluation: "AI 批改暫時不可用",
                    strengths: "請檢查你的答案是否符合題目要求",
                    improvements: "可以參考提示和參考答案進行自我檢查"
                };
            }
        }

        // 處理批改按鈕點擊事件
        async function handleGrade() {
            const userAnswer = userAnswerEl.value.trim();
            if (!userAnswer) {
                // 改用自訂的提示框代替 alert
                showCustomAlert('請先輸入答案！');
                return;
            }

            gradeBtn.disabled = true;
            gradeBtn.textContent = '批改中...';
            showLoader();

            try {
                const feedback = await getAIFeedback(userAnswer);
                showFeedback(feedback);
            } catch (error) {
                handleApiError(error);
            } finally {
                gradeBtn.disabled = false;
                gradeBtn.textContent = 'AI 批改';
            }
        }

        // 顯示自訂提示框
        function showCustomAlert(message) {
            // 避免使用 alert()，改用 feedback 區域顯示提示
            feedbackSection.innerHTML = `
                <div class="feedback-card bg-yellow-50 border-yellow-200">
                    <p class="text-yellow-800 font-semibold">${message}</p>
                </div>
            `;
        }


        // --- 事件監聽器 ---
        tabRewrite.addEventListener('click', () => switchTab('rewrite'));
        tabExpand.addEventListener('click', () => switchTab('expand'));
        gradeBtn.addEventListener('click', handleGrade);
        nextBtn.addEventListener('click', displayNewQuestion);

        // --- 初始化 ---
        window.onload = () => {
            switchTab('rewrite');
        };
    </script>

</body>
</html>
