<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小五中文測驗卷 (分頁互動版)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@latest/Sortable.min.js"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+TC:wght@400;500;700&display=swap');
        html, body {
            height: 100%;
            overflow: hidden; /* Prevent main scroll */
        }
        body {
            font-family: 'Noto Sans TC', sans-serif;
            background-color: #f0f2f5;
            display: flex;
            flex-direction: column;
        }
        .main-container {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem; /* Reduced padding for mobile */
            display: flex;
            flex-direction: column;
        }
        #quiz-container {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }
        .question-card {
            background-color: white;
            border-radius: 12px;
            padding: 1rem; /* Reduced padding for mobile */
            margin-bottom: 1rem; /* Reduced margin */
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .question-title {
            font-size: 1.1rem; /* Slightly smaller title */
            font-weight: 700;
            color: #1e3a8a;
            margin-bottom: 1rem;
            border-left: 4px solid #3b82f6;
            padding-left: 0.75rem;
        }
        .question-text {
            color: #374151;
            line-height: 1.7; /* Adjusted line height */
            margin-bottom: 0.75rem;
        }
        .option-label {
            display: block;
            padding: 0.75rem; /* Reduced padding */
            border: 1px solid #d1d5db;
            border-radius: 8px;
            margin-bottom: 0.5rem;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .option-label:hover {
            background-color: #eff6ff;
            border-color: #60a5fa;
        }
        input[type="radio"]:checked + .option-label {
            background-color: #dbeafe;
            border-color: #3b82f6;
            font-weight: 500;
        }
        .nav-btn {
            background-color: #ffffff;
            color: #3b82f6;
            font-weight: 600; /* Adjusted font weight */
            padding: 0.5rem 1rem; /* Reduced padding */
            font-size: 0.875rem; /* Reduced font size */
            border-radius: 8px;
            cursor: pointer;
            border: 1px solid #3b82f6;
            transition: all 0.3s ease;
        }
        .nav-btn:hover {
            background-color: #eff6ff;
        }
        .nav-btn:disabled {
            background-color: #e5e7eb;
            color: #9ca3af;
            border-color: #d1d5db;
            cursor: not-allowed;
        }
        .submit-btn {
            background-color: #2563eb;
            color: white;
            font-weight: 700;
            padding: 0.6rem 1.5rem; /* Reduced padding */
            font-size: 0.9rem; /* Reduced font size */
            border-radius: 8px;
            cursor: pointer;
            transition: background-color 0.3s ease;
            width: 100%;
        }
        .submit-btn:hover {
            background-color: #1d4ed8;
        }
        footer {
            padding: 0.5rem; /* Reduced footer padding */
        }
        .reading-article {
            background-color: #f8fafc;
            padding: 1rem;
            border-radius: 8px;
            border: 1px solid #e2e8f0;
            line-height: 1.8;
        }
        .page { display: none; height: 100%; }
        .page.active {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            min-height: 0;
        }
        
        .reorder-option {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 8px;
            cursor: pointer;
            background-color: white;
            user-select: none;
        }
        .reorder-option.disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        .reorder-answer-display {
            min-height: 58px;
            background-color: #f9fafb;
            border: 1px dashed #d1d5db;
            border-radius: 8px;
            padding: 10px;
        }
        .reorder-answer-item { cursor: grab; }
        .reorder-answer-item:active { cursor: grabbing; }
        .sortable-ghost { opacity: 0.4; background: #c7d2fe; }

        .punc-preview {
            color: #2563eb;
            font-weight: 700;
            padding: 0 0.25rem;
            display: inline-block;
            min-width: 1.5rem;
            text-align: center;
        }

        /* Reading Comprehension Mobile Layout */
        #rc-page-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            flex-grow: 1; /* Allow card to grow */
            min-height: 0;
        }
        #rc-article-container {
            flex-shrink: 0;
            height: 50%;
            overflow-y: auto;
        }
        #rc-questions-container {
            flex-shrink: 0;
            height: 50%;
            display: flex;
            flex-direction: column;
            padding-top: 0.75rem; /* Reduced padding */
            min-height: 0;
        }
        #rc-questions-wrapper {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0;
            position: relative;
        }
        .rc-question {
            display: none; /* Hide questions by default */
            overflow-y: auto;
            padding-right: 0.5rem; /* for scrollbar */
        }
        .rc-question.active {
            display: block; /* Show active question */
            flex: 1;
            min-height: 0;
        }
        /* Desktop Layout Override */
        @media (min-width: 1024px) {
            .main-container { padding: 1rem; }
            .question-card { padding: 24px; margin-bottom: 24px; }
            .question-title { font-size: 1.25rem; }
            .nav-btn { padding: 12px 24px; font-size: 1rem; }
            .submit-btn { padding: 12px 24px; font-size: 1rem; }
            footer { padding: 1rem; }
            
            html, body {
                height: auto;
                overflow: auto;
            }
            #rc-page-container {
                flex-direction: row;
                height: auto;
                gap: 2rem;
            }
            #rc-article-container {
                width: 50%;
                height: auto;
                position: sticky;
                top: 1rem;
                max-height: 90vh;
            }
            #rc-questions-container {
                width: 50%;
                height: auto;
                padding-top: 0;
            }
            #rc-questions-wrapper {
               display: block;
               flex: none;
            }
            .rc-question {
                display: block; /* Show all questions on desktop */
                flex: none;
                margin-bottom: 1.5rem;
                overflow-y: visible;
                padding-right: 0;
            }
            #rc-nav {
                display: none;
            }
        }
        .form-input {
            width: 100%;
            border-radius: 8px;
            border: 1px solid #d1d5db;
            padding: 8px 12px;
            transition: border-color 0.2s ease;
        }
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 2px #dbeafe;
        }
    </style>
</head>
<body>
    <div class="main-container">
        <header class="text-center mb-4">
            <h1 class="text-2xl md:text-4xl font-bold text-gray-800">小五中文測驗卷</h1>
            <p class="text-gray-500 mt-1 md:mt-2">分頁互動版</p>
        </header>

        <main id="quiz-container" class="flex-grow">
            <!-- Page 1: 詞語選擇 -->
            <div class="page">
                <div class="question-card max-w-4xl mx-auto">
                    <h3 class="question-title">一、詞語選擇 (每題2分)</h3>
                    <div class="space-y-6">
                        <div>
                            <p class="question-text">1. 我有疼愛我的爸爸、媽媽，還有一個感情濃厚、關係密切的好哥哥。</p>
                            <div id="q1-1">
                                <input type="radio" name="q1-1" value="C" id="q1-1-C" class="hidden"> <label for="q1-1-C" class="option-label">C. 相親相愛</label>
                                <input type="radio" name="q1-1" value="A" id="q1-1-A" class="hidden"> <label for="q1-1-A" class="option-label">A. 相依為命</label>
                                <input type="radio" name="q1-1" value="D" id="q1-1-D" class="hidden"> <label for="q1-1-D" class="option-label">D. 相敬如賓</label>
                            </div>
                        </div>
                        <div>
                            <p class="question-text">2. 他學識淺薄，卻經常到處發表言論，故意做作，裝出某種腔調給人看，讓人感到嫌惡。</p>
                            <div id="q1-2">
                                <input type="radio" name="q1-2" value="A" id="q1-2-A" class="hidden"> <label for="q1-2-A" class="option-label">A. 裝腔作勢</label>
                                <input type="radio" name="q1-2" value="B" id="q1-2-B" class="hidden"> <label for="q1-2-B" class="option-label">B. 裝神弄鬼</label>
                                <input type="radio" name="q1-2" value="D" id="q1-2-D" class="hidden"> <label for="q1-2-D" class="option-label">D. 虛張聲勢</label>
                            </div>
                        </div>
                         <div>
                            <p class="question-text">3. 上海是一個經濟繁榮的大都市，來自世界各地經商的人來來往往，接連不斷。</p>
                            <div id="q1-3">
                                <input type="radio" name="q1-3" value="B" id="q1-3-B" class="hidden"> <label for="q1-3-B" class="option-label">B. 滔滔不絕</label>
                                <input type="radio" name="q1-3" value="D" id="q1-3-D" class="hidden"> <label for="q1-3-D" class="option-label">D. 絡繹不絕</label>
                                <input type="radio" name="q1-3" value="C" id="q1-3-C" class="hidden"> <label for="q1-3-C" class="option-label">C. 接二連三</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page 2: 量詞運用 -->
            <div class="page">
                <div class="question-card max-w-4xl mx-auto">
                    <h3 class="question-title">二、量詞運用 (每個答案1分)</h3>
                    <div class="space-y-8">
                        <div>
                            <p class="question-text">1. 王子舉辦了一 ___ 盛大的舞會，邀請城中所有成年女子出席。</p>
                            <div id="q2-1-text" class="flex space-x-2 mb-2">
                                <input type="radio" name="q2-1-text" value="場" id="q2-1-text-A" class="hidden"><label for="q2-1-text-A" class="option-label flex-1 text-center">場</label>
                                <input type="radio" name="q2-1-text" value="次" id="q2-1-text-B" class="hidden"><label for="q2-1-text-B" class="option-label flex-1 text-center">次</label>
                                <input type="radio" name="q2-1-text" value="回" id="q2-1-text-C" class="hidden"><label for="q2-1-text-C" class="option-label flex-1 text-center">回</label>
                            </div>
                            <div id="q2-1-type" class="flex space-x-2">
                                <input type="radio" name="q2-1-type" value="物" id="q2-1-type-A" class="hidden"><label for="q2-1-type-A" class="option-label flex-1 text-center">物量詞</label>
                                <input type="radio" name="q2-1-type" value="動" id="q2-1-type-B" class="hidden"><label for="q2-1-type-B" class="option-label flex-1 text-center">動量詞</label>
                            </div>
                        </div>
                        <div>
                            <p class="question-text">2. 灰姑娘親手做了一 ___ 純白色的連衣裙，打算穿着它參加舞會。</p>
                             <div id="q2-2-text" class="flex space-x-2 mb-2">
                                <input type="radio" name="q2-2-text" value="件" id="q2-2-text-A" class="hidden"><label for="q2-2-text-A" class="option-label flex-1 text-center">件</label>
                                <input type="radio" name="q2-2-text" value="條" id="q2-2-text-B" class="hidden"><label for="q2-2-text-B" class="option-label flex-1 text-center">條</label>
                                <input type="radio" name="q2-2-text" value="套" id="q2-2-text-C" class="hidden"><label for="q2-2-text-C" class="option-label flex-1 text-center">套</label>
                            </div>
                            <div id="q2-2-type" class="flex space-x-2">
                                <input type="radio" name="q2-2-type" value="物" id="q2-2-type-A" class="hidden"><label for="q2-2-type-A" class="option-label flex-1 text-center">物量詞</label>
                                <input type="radio" name="q2-2-type" value="動" id="q2-2-type-B" class="hidden"><label for="q2-2-type-B" class="option-label flex-1 text-center">動量詞</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page 3: 句子排序 (一) -->
            <div class="page">
                <div class="question-card max-w-4xl mx-auto">
                    <h3 class="question-title">三、句子排序 (一) (4分)</h3>
                    <p class="question-text mb-4">請點擊下方字母，將句子按正確順序排列。</p>
                    <div class="text-gray-600 bg-gray-50 p-4 rounded-lg space-y-1 mb-4">
                        <p>A. 無拘無束地談天說地。</p>
                        <p>B. 漣漪在陽光的照耀下，</p>
                        <p>C. 我和表哥坐在湖邊的草地上，</p>
                        <p>D. 像一條條光彩悅目的綢子。</p>
                        <p>E. 水面漾起了一圈圈漣漪。</p>
                        <p>F. 看着眼前美麗的景色，</p>
                        <p>G. 忽然，幾隻燕子掠過湖面，</p>
                        <p>H. 我心想：「如果能把這美好的時光永遠留住便好了。」</p>
                    </div>
                    <p class="font-semibold">選項：</p>
                    <div id="q3-1-options" class="flex flex-wrap gap-2 my-2">
                        <div class="reorder-option" data-value="A">A</div> <div class="reorder-option" data-value="B">B</div>
                        <div class="reorder-option" data-value="C">C</div> <div class="reorder-option" data-value="D">D</div>
                        <div class="reorder-option" data-value="E">E</div> <div class="reorder-option" data-value="F">F</div>
                        <div class="reorder-option" data-value="G">G</div> <div class="reorder-option" data-value="H">H</div>
                    </div>
                    <p class="font-semibold mt-4">你的答案：(可拖曳調整順序，點擊可移除)</p>
                    <div id="q3-1-answer-display" class="reorder-answer-display flex flex-wrap gap-2 items-center"></div>
                    <input type="hidden" id="q3-1-answer">
                </div>
            </div>

            <!-- Page 4: 句子排序 (二) -->
            <div class="page">
                <div class="question-card max-w-4xl mx-auto">
                    <h3 class="question-title">四、句子排序 (二) (4分)</h3>
                    <p class="question-text mb-4">請點擊下方字母，將句子按正確順序排列。</p>
                    <div class="text-gray-600 bg-gray-50 p-4 rounded-lg space-y-1 mb-4">
                        <p>A. 站在山巔上，腳下是萬丈深谷，眼前是連綿不絕的羣山，壯麗極了!</p>
                        <p>B. 黃昏快將來臨，雲海依舊那麼平靜。</p>
                        <p>C. 經過艱苦的行程，我們終於登上峨嵋山山巔。</p>
                        <p>D. 雲海是這樣白，這樣靜，天地都給籠罩起來。</p>
                        <p>E. 暑假裏，媽媽帶我到峨嵋山觀賞雲霧。</p>
                        <p>F. 不一會兒，我們的腳下成了一片茫茫大海 — 由雲和霧組成的雲海。</p>
                        <p>G. 忽然，一團雲霧奔騰而來，漸漸地吞沒了四周的山巒。</p>
                        <p>H. 到了離開的時候，我依然陶醉在峨嵋山的雲海中，依依不捨。</p>
                    </div>
                    <p class="font-semibold">選項：</p>
                    <div id="q4-1-options" class="flex flex-wrap gap-2 my-2">
                        <div class="reorder-option" data-value="A">A</div> <div class="reorder-option" data-value="B">B</div>
                        <div class="reorder-option" data-value="C">C</div> <div class="reorder-option" data-value="D">D</div>
                        <div class="reorder-option" data-value="E">E</div> <div class="reorder-option" data-value="F">F</div>
                        <div class="reorder-option" data-value="G">G</div> <div class="reorder-option" data-value="H">H</div>
                    </div>
                    <p class="font-semibold mt-4">你的答案：(可拖曳調整順序，點擊可移除)</p>
                    <div id="q4-1-answer-display" class="reorder-answer-display flex flex-wrap gap-2 items-center"></div>
                    <input type="hidden" id="q4-1-answer">
                </div>
            </div>

            <!-- Page 5: 重組句子 -->
            <div class="page">
                <div class="question-card max-w-4xl mx-auto">
                    <h3 class="question-title">五、重組句子 (4分)</h3>
                    <p class="question-text mb-4">請點擊下方的詞語，把它們組合成一個通順的句子。</p>
                    <p class="font-semibold">選項：</p>
                    <div id="q5-1-options" class="flex flex-wrap gap-2 my-2">
                        <div class="reorder-option" data-value="自由地">自由地</div>
                        <div class="reorder-option" data-value="小鳥">小鳥</div>
                        <div class="reorder-option" data-value="飛翔">飛翔</div>
                        <div class="reorder-option" data-value="在">在</div>
                        <div class="reorder-option" data-value="。">。</div>
                        <div class="reorder-option" data-value="藍天上">藍天上</div>
                    </div>
                    <p class="font-semibold mt-4">你的答案：(可拖曳調整順序，點擊可移除)</p>
                    <div id="q5-1-answer-display" class="reorder-answer-display flex flex-wrap gap-2 items-center"></div>
                    <input type="hidden" id="q5-1-answer">
                </div>
            </div>

            <!-- Page 6: 標點符號 -->
            <div class="page">
                 <div class="question-card max-w-4xl mx-auto">
                    <h3 class="question-title">六、標點符號 (6分)</h3>
                    <p class="question-text mb-4">請為下列句子選擇最適合的標點符號。</p>
                    <div id="q6-1">
                        <p id="punc-sentence" class="question-text text-xl bg-gray-50 p-4 rounded-md">春天的時候，花園裏繁花似錦<span id="punc-preview-1" class="punc-preview">[ 1 ]</span>冬天的時候，花園裏枝葉零落<span id="punc-preview-2" class="punc-preview">[ 2 ]</span></p>
                        <div class="mt-6">
                            <p class="font-semibold">標點 [ 1 ] 應為：</p>
                            <div id="q6-1-1" class="flex space-x-2 my-2">
                                <input type="radio" name="q6-1-1" value="，" id="q6-1-1A" class="hidden"><label for="q6-1-1A" class="option-label flex-1 text-center text-2xl">，</label>
                                <input type="radio" name="q6-1-1" value="；" id="q6-1-1B" class="hidden"><label for="q6-1-1B" class="option-label flex-1 text-center text-2xl">；</label>
                                <input type="radio" name="q6-1-1" value="。" id="q6-1-1C" class="hidden"><label for="q6-1-1C" class="option-label flex-1 text-center text-2xl">。</label>
                            </div>
                        </div>
                        <div class="mt-6">
                            <p class="font-semibold">標點 [ 2 ] 應為：</p>
                            <div id="q6-1-2" class="flex space-x-2 my-2">
                                <input type="radio" name="q6-1-2" value="，" id="q6-1-2A" class="hidden"><label for="q6-1-2A" class="option-label flex-1 text-center text-2xl">，</label>
                                <input type="radio" name="q6-1-2" value="；" id="q6-1-2B" class="hidden"><label for="q6-1-2B" class="option-label flex-1 text-center text-2xl">；</label>
                                <input type="radio" name="q6-1-2" value="。" id="q6-1-2C" class="hidden"><label for="q6-1-2C" class="option-label flex-1 text-center text-2xl">。</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page 7: 閱讀理解 (Responsive Layout) -->
            <div class="page active">
                <div id="rc-page-container" class="question-card">
                    <!-- Left/Top Column: Article -->
                    <div id="rc-article-container">
                         <article class="reading-article h-full">
                            <h3 class="question-title">七、閱讀理解</h3>
                            <p class="font-bold mb-2">斯特拉斯堡的聖誕</p>
                            <p>去年冬天，爸爸媽媽帶我到法國的斯特拉斯堡旅遊，度過了一個難忘的聖誕。斯特拉斯堡位於法國東部，毗鄰萊茵河。每年聖誕節前後，這個城市都會舉辦大大小小的聖誕集市，吸引成千上萬的遊客前來拜訪。</p>
                            <p>聖誕節那天中午，我們抵達斯特拉斯堡，在酒店安放好行李後，就迫不及待地前往行程的第一站————克勒貝爾廣場。一踏進廣場，一棵巨大的聖誕樹映入我們的眼簾，樹下的遊人成了一個個小人國國民，十分有趣。廣場上滿佈不同種類的攤子，售賣蔬果、熟食、日用品等。每個攤子都像一座小木屋，各自有獨特的裝飾。我們邊走邊看，忽然一陣香噴噴的味道撲鼻而來。我們循着香味尋找源頭，啊！原來是賣烤雞的攤子傳來的。我們買了一隻分着吃，在冷颼颼的寒風中品嘗熱騰騰的美食，真是既舒暢又滿足。</p>
                            <p>吃完後，我們沿着小路走，忽見遠處一個尖尖的粉紅色樓頂直指雲霄，彷彿與天際相接，原來那就是鼎鼎大名的斯特拉斯堡大教堂。這座教堂已在這裏屹立幾百年，外牆的雕刻讓人歎為觀止。進入教堂後，我不禁深深地吸了一口氣 高高的樓頂下有無數五彩繽紛的彩繪玻璃和巧奪天工的雕像。教堂氣氛莊嚴神聖，身處其中，連平日多話的我也不敢做聲。</p>
                            <p>參觀完教堂，我們到教堂外的聖誕集市閒逛。集市排列着密密麻麻的攤子，遊人摩肩接踵，好不熱鬧，跟教堂內的肅穆、安寧截然不同。我蹦蹦跳跳地穿梭其中，各式各樣的聖誕裝飾、木製精品、餅乾糖果，令人目不暇給。</p>
                            <p>近黃昏時，我們到達這天旅程的最後一站—————小法國區。這裏屬於舊城區，一棟棟色彩絢麗的中世紀木屋依河而建。木屋的外牆縱橫交錯着一根根木條，加上石板小路上的古老招牌和瑰麗的聖誕裝飾，令人恍若置身於童話世界。天色慢慢暗下來，城市的燈光漸漸亮起。我們漫步至規模細小的二手集市，攤子柔和的燈光為集市添上點點溫馨和浪漫。跟白天相比，又是另一番景致。</p>
                            <p>離開斯特拉斯堡那天,我坐在火車上,看着這座美麗的城市漸漸遠去,心中惘然若失。這裏如夢如幻的聖誕氣氛會長留在我心中。</p>
                        </article>
                    </div>
                    <!-- Right/Bottom Column: Questions -->
                    <div id="rc-questions-container">
                        <div id="rc-questions-wrapper">
                            <div class="rc-question active">
                                <p class="question-text">1. 從文中找出適當的詞語，使句子的意思完整：沙田獅子山上 ___ 着一塊巨石... (2分)</p>
                                 <div class="flex space-x-2">
                                    <input type="radio" name="q7-1" value="屹立" id="q7-1-A" class="hidden"><label for="q7-1-A" class="option-label flex-1 text-center">屹立</label>
                                    <input type="radio" name="q7-1" value="站立" id="q7-1-B" class="hidden"><label for="q7-1-B" class="option-label flex-1 text-center">站立</label>
                                    <input type="radio" name="q7-1" value="放置" id="q7-1-C" class="hidden"><label for="q7-1-C" class="option-label flex-1 text-center">放置</label>
                                 </div>
                            </div>
                            <div class="rc-question">
                                <p class="question-text">2. 為甚麼「我」在斯特拉斯堡大教堂裏不敢做聲？(4分)</p>
                                <textarea id="q7-2-textarea" class="form-input" rows="4" placeholder="請在此輸入你的答案..."></textarea>
                            </div>
                            <div class="rc-question">
                                <p class="question-text">3. 下列哪一項不是斯特拉斯堡大教堂的特色？(2分)</p>
                                <div>
                                    <input type="radio" name="q7-3" value="A" id="q7-3-A" class="hidden"> <label for="q7-3-A" class="option-label">A. 歷史悠久。</label>
                                    <input type="radio" name="q7-3" value="C" id="q7-3-C" class="hidden"> <label for="q7-3-C" class="option-label">C. 外牆色彩繽紛。</label>
                                    <input type="radio" name="q7-3" value="D" id="q7-3-D" class="hidden"> <label for="q7-3-D" class="option-label">D. 內外都有精緻的雕刻。</label>
                                </div>
                            </div>
                        </div>
                        <div id="rc-nav" class="mt-4 flex justify-between items-center">
                            <button id="rc-prev-btn" class="nav-btn">上一題</button>
                            <div id="rc-page-indicator" class="text-gray-600"></div>
                            <button id="rc-next-btn" class="nav-btn">下一題</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Result container -->
            <div id="result-container" class="hidden mt-8 p-6 bg-blue-100 border border-blue-300 rounded-lg text-center max-w-4xl mx-auto">
                <h2 class="text-2xl font-bold text-blue-800">測驗結果</h2>
                <p id="score" class="text-4xl font-bold text-blue-600 my-4"></p>
                <p id="feedback" class="text-lg text-blue-700"></p>
            </div>
        </main>
    </div>
    
    <!-- Main Navigation -->
    <footer class="p-4">
        <div class="flex justify-between items-center max-w-4xl mx-auto">
            <button id="prev-btn" class="nav-btn">上一頁</button>
            <div id="page-indicator" class="text-gray-600"></div>
            <button id="next-btn" class="nav-btn">下一頁</button>
        </div>
        <button id="submit-btn" class="submit-btn mt-4 hidden max-w-4xl mx-auto">提交答案</button>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // --- DOM Elements ---
            const pages = document.querySelectorAll('.page');
            const prevBtn = document.getElementById('prev-btn');
            const nextBtn = document.getElementById('next-btn');
            const submitBtn = document.getElementById('submit-btn');
            const pageIndicator = document.getElementById('page-indicator');
            
            // --- State ---
            let currentPageIndex = 0;
            const totalPages = pages.length;

            // --- Answers and Points ---
            const answers = {
                'q1-1': 'C', 'q1-2': 'A', 'q1-3': 'D',
                'q2-1-text': '場', 'q2-1-type': '動',
                'q2-2-text': '條', 'q2-2-type': '物',
                'q3-1': 'CGFEBDHA',
                'q4-1': 'ECAGFBDH',
                'q5-1': '小鳥在藍天上自由地飛翔。',
                'q6-1-1': '；', 'q6-1-2': '。',
                'q7-1': '屹立', 'q7-2': '莊嚴神聖', 'q7-3': 'C',
            };
            const points = {
                'q1': 6, 'q2': 4, 'q3': 4, 'q4': 4, 'q5': 4, 'q6': 6, 'q7': 8
            };
            const totalPoints = Object.values(points).reduce((s, p) => s + p, 0);

            // --- Functions ---
            function updatePage() {
                pages.forEach((page, index) => {
                    page.classList.toggle('active', index === currentPageIndex);
                });
                pageIndicator.textContent = `第 ${currentPageIndex + 1} / ${totalPages} 頁`;
                prevBtn.disabled = currentPageIndex === 0;
                nextBtn.classList.toggle('hidden', currentPageIndex === totalPages - 1);
                submitBtn.classList.toggle('hidden', currentPageIndex !== totalPages - 1);
                 // Hide footer when result is shown
                document.querySelector('footer').style.display = submitBtn.disabled ? 'none' : 'block';
            }

            function setupReorderQuestion(questionId) {
                const optionsContainer = document.getElementById(`${questionId}-options`);
                const answerDisplay = document.getElementById(`${questionId}-answer-display`);
                const answerInput = document.getElementById(`${questionId}-answer`);

                if (!optionsContainer || !answerDisplay || !answerInput) return;

                const updateAnswerInput = () => {
                    const items = answerDisplay.querySelectorAll('.reorder-answer-item');
                    const answer = Array.from(items).map(item => item.dataset.value).join('');
                    answerInput.value = answer;
                };

                optionsContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('reorder-option') && !e.target.classList.contains('disabled')) {
                        const optionElement = e.target;
                        const value = optionElement.dataset.value;
                        optionElement.classList.add('disabled');

                        const answerItem = document.createElement('div');
                        answerItem.className = 'reorder-answer-item reorder-option';
                        answerItem.textContent = value;
                        answerItem.dataset.value = value;
                        
                        answerItem.addEventListener('click', () => {
                            answerItem.remove();
                            optionElement.classList.remove('disabled');
                            updateAnswerInput();
                        });

                        answerDisplay.appendChild(answerItem);
                        updateAnswerInput();
                    }
                });

                new Sortable(answerDisplay, {
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    onEnd: function (evt) {
                        updateAnswerInput();
                    },
                });
            }

            function setupPunctuationPreview() {
                const punc1Radios = document.querySelectorAll('input[name="q6-1-1"]');
                const punc2Radios = document.querySelectorAll('input[name="q6-1-2"]');
                const preview1 = document.getElementById('punc-preview-1');
                const preview2 = document.getElementById('punc-preview-2');

                punc1Radios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        preview1.textContent = this.value;
                    });
                });

                punc2Radios.forEach(radio => {
                    radio.addEventListener('change', function() {
                        preview2.textContent = this.value;
                    });
                });
            }

            function setupRCNavigation() {
                const rcQuestions = document.querySelectorAll('.rc-question');
                const rcPrevBtn = document.getElementById('rc-prev-btn');
                const rcNextBtn = document.getElementById('rc-next-btn');
                const rcIndicator = document.getElementById('rc-page-indicator');
                let currentRCIndex = 0;
                const totalRCQuestions = rcQuestions.length;

                function updateRCQuestion() {
                    rcQuestions.forEach((q, index) => {
                        q.classList.toggle('active', index === currentRCIndex);
                    });
                    rcIndicator.textContent = `題目 ${currentRCIndex + 1} / ${totalRCQuestions}`;
                    rcPrevBtn.disabled = currentRCIndex === 0;
                    rcNextBtn.disabled = currentRCIndex === totalRCQuestions - 1;
                }

                rcPrevBtn.addEventListener('click', () => {
                    if (currentRCIndex > 0) {
                        currentRCIndex--;
                        updateRCQuestion();
                    }
                });

                rcNextBtn.addEventListener('click', () => {
                    if (currentRCIndex < totalRCQuestions - 1) {
                        currentRCIndex++;
                        updateRCQuestion();
                    }
                });
                updateRCQuestion();
            }

            // --- Event Listeners ---
            nextBtn.addEventListener('click', () => {
                if (currentPageIndex < totalPages - 1) {
                    currentPageIndex++;
                    updatePage();
                }
            });

            prevBtn.addEventListener('click', () => {
                if (currentPageIndex > 0) {
                    currentPageIndex--;
                    updatePage();
                }
            });
            
            submitBtn.addEventListener('click', () => {
                let score = 0;
                
                // Q1
                if (document.querySelector('input[name="q1-1"]:checked')?.value === answers['q1-1']) score += 2;
                if (document.querySelector('input[name="q1-2"]:checked')?.value === answers['q1-2']) score += 2;
                if (document.querySelector('input[name="q1-3"]:checked')?.value === answers['q1-3']) score += 2;

                // Q2
                if (document.querySelector('input[name="q2-1-text"]:checked')?.value === answers['q2-1-text']) score += 1;
                if (document.querySelector('input[name="q2-1-type"]:checked')?.value === answers['q2-1-type']) score += 1;
                if (document.querySelector('input[name="q2-2-text"]:checked')?.value === answers['q2-2-text']) score += 1;
                if (document.querySelector('input[name="q2-2-type"]:checked')?.value === answers['q2-2-type']) score += 1;

                // Q3 - Sentence Ordering 1
                if (document.getElementById('q3-1-answer').value === answers['q3-1']) score += points['q3'];
                
                // Q4 - Sentence Ordering 2
                if (document.getElementById('q4-1-answer').value === answers['q4-1']) score += points['q4'];

                // Q5 - Word Jumble
                if (document.getElementById('q5-1-answer').value === answers['q5-1']) score += points['q5'];

                // Q6 - Punctuation
                if (document.querySelector('input[name="q6-1-1"]:checked')?.value === answers['q6-1-1']) score += 3;
                if (document.querySelector('input[name="q6-1-2"]:checked')?.value === answers['q6-1-2']) score += 3;
                
                // Q7 - Reading Comprehension
                if(document.querySelector('input[name="q7-1"]:checked')?.value === answers['q7-1']) score += 2;
                const q7_2_answer = document.getElementById('q7-2-textarea').value;
                if(q7_2_answer && answers['q7-2'].split('').every(char => q7_2_answer.includes(char))) score += 4;
                if(document.querySelector('input[name="q7-3"]:checked')?.value === answers['q7-3']) score += 2;

                // --- Display results ---
                const resultContainer = document.getElementById('result-container');
                document.getElementById('score').textContent = `${score} / ${totalPoints} 分`;
                const percentage = (score / totalPoints) * 100;
                let feedbackText = '';
                if (percentage === 100) feedbackText = '太棒了，全部正確！';
                else if (percentage >= 80) feedbackText = '表現優異，繼續努力！';
                else if (percentage >= 60) feedbackText = '成績不錯，還有進步空間。';
                else feedbackText = '要多加溫習哦，加油！';
                document.getElementById('feedback').textContent = feedbackText;
                
                document.querySelector('.main-container').scrollTo(0, 0);
                document.getElementById('quiz-container').classList.add('hidden');
                resultContainer.classList.remove('hidden');

                // Disable buttons and hide footer
                submitBtn.disabled = true;
                submitBtn.textContent = '已完成';
                updatePage();
            });

            // --- Initial Setup ---
            setupReorderQuestion('q3-1');
            setupReorderQuestion('q4-1');
            setupReorderQuestion('q5-1');
            setupPunctuationPreview();
            setupRCNavigation();
            updatePage();
        });
    </script>
</body>
</html>
