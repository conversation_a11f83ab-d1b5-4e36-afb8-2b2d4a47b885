exports.handler = async (event, context) => {
  // 只允許 POST 請求
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  // 處理 CORS 預檢請求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: ''
    };
  }

  try {
    // 解析請求資料
    const { userAnswer, originalSentence, hint, modelAnswer, exerciseType } = JSON.parse(event.body);
    
    // 驗證必要參數
    if (!userAnswer || !originalSentence || !exerciseType) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ error: '缺少必要參數' })
      };
    }

    // 從環境變數獲取 SiliconFlow API 金鑰
    const apiKey = process.env.SILICONFLOW_API_KEY;

    if (!apiKey) {
      console.error('SILICONFLOW_API_KEY not found in environment variables');
      return {
        statusCode: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ error: 'API 配置錯誤' })
      };
    }

    // 構建 AI 提示
    const prompt = `你是一位專業且有耐心的香港小學中文老師。你的任務是根據學生的答案提供有建設性的回饋。

練習類型：${exerciseType}
原句：${originalSentence}
提示：${hint || '無特別提示'}
參考答案：${modelAnswer || '無參考答案'}
學生答案：${userAnswer}

請根據以下要求，用繁體中文生成一個 JSON 物件作為回饋。參考答案僅為一個例子，任何合理、通順且符合提示要求的答案都應被鼓勵。

請直接回覆 JSON 格式，不要包含任何其他文字：
{
  "evaluation": "（對學生的答案做一個總體評價，語氣要正面鼓勵，例如：做得很好！句子通順，完全符合提示的要求。）",
  "strengths": "（具體指出學生答案的優點，例如：能準確運用「雖然...但...」的句式，使句子意思更完整。）",
  "improvements": "（提出具體的改進建議，如果沒有可以改進的地方，請寫「句子通順，沒有需要修改的地方。」例如：可以嘗試使用更生動的詞語，讓句子更有畫面感。）"
}`;

    // 準備 SiliconFlow API 請求資料
    const payload = {
      model: "Qwen/Qwen2.5-7B-Instruct",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    };

    const apiUrl = `https://api.siliconflow.cn/v1/chat/completions`;

    // 實作指數退避重試
    let response;
    let attempts = 0;
    const maxAttempts = 3;
    let delay = 1000;

    while (attempts < maxAttempts) {
      try {
        console.log(`Attempt ${attempts + 1} to call Gemini API`);
        
        response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify(payload)
        });

        if (response.ok) {
          const result = await response.json();
          console.log('SiliconFlow API response received');

          if (result.choices && result.choices.length > 0 &&
              result.choices[0].message && result.choices[0].message.content) {

            const text = result.choices[0].message.content.trim();
            console.log('Parsing AI response:', text);

            try {
              // 嘗試直接解析 JSON
              const feedback = JSON.parse(text);
              return {
                statusCode: 200,
                headers: {
                  'Access-Control-Allow-Origin': '*',
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(feedback)
              };
            } catch (parseError) {
              console.error('Failed to parse AI response as JSON:', parseError);
              console.log('Raw response text:', text);

              // 嘗試提取 JSON 部分
              const jsonMatch = text.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                try {
                  const feedback = JSON.parse(jsonMatch[0]);
                  return {
                    statusCode: 200,
                    headers: {
                      'Access-Control-Allow-Origin': '*',
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(feedback)
                  };
                } catch (secondParseError) {
                  console.error('Second JSON parse attempt failed:', secondParseError);
                }
              }

              // 如果 JSON 解析失敗，返回預設回饋
              return {
                statusCode: 200,
                headers: {
                  'Access-Control-Allow-Origin': '*',
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  evaluation: "你的答案很不錯！",
                  strengths: "句子結構完整，表達清楚。",
                  improvements: "繼續保持這樣的寫作水準！"
                })
              };
            }
          }
        } else {
          const errorText = await response.text();
          console.error(`API request failed with status ${response.status}:`, errorText);
        }
        
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error.message);
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        console.log(`Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // 指數增加延遲時間
      }
    }
    
    // 所有重試都失敗，返回錯誤
    console.error('All API attempts failed');
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        error: 'AI 批改服務暫時不可用，請稍後再試',
        fallback: {
          evaluation: "暫時無法提供 AI 批改",
          strengths: "請檢查你的答案是否符合題目要求",
          improvements: "可以參考提示再次嘗試"
        }
      })
    };
    
  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        error: '伺服器內部錯誤',
        message: error.message 
      })
    };
  }
};
