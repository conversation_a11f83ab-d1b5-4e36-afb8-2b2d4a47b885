exports.handler = async (event, context) => {
  // 只允許 POST 請求
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: JSON.stringify({ error: 'Method not allowed' })
    };
  }

  // 處理 CORS 預檢請求
  if (event.httpMethod === 'OPTIONS') {
    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      },
      body: ''
    };
  }

  try {
    // 解析請求資料
    const { userAnswer, originalSentence, hint, modelAnswer, exerciseType } = JSON.parse(event.body);
    
    // 驗證必要參數
    if (!userAnswer || !originalSentence || !exerciseType) {
      return {
        statusCode: 400,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ error: '缺少必要參數' })
      };
    }

    // 從環境變數獲取 SiliconFlow API 金鑰
    const apiKey = process.env.SILICONFLOW_API_KEY;

    if (!apiKey) {
      console.error('SILICONFLOW_API_KEY not found in environment variables');
      return {
        statusCode: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ error: 'API 配置錯誤' })
      };
    }

    // 構建 AI 提示
    const prompt = `你是一位專業且嚴格的香港小學中文老師。你的任務是仔細檢查學生的答案是否符合題目要求，並提供準確的回饋。

練習類型：${exerciseType}
原句：${originalSentence}
提示：${hint || '無特別提示'}
參考答案：${modelAnswer || '無參考答案'}
學生答案：${userAnswer}

請仔細分析學生答案是否：
1. 符合題目的具體要求（如改寫句式、擴寫內容等）
2. 保持原句的基本意思
3. 語法正確且通順

評分標準：
- 如果學生答案完全不相關或不符合題目要求，請明確指出問題
- 如果學生答案部分正確但有改進空間，請具體說明
- 只有當答案確實符合要求且表達良好時，才給予正面評價

請直接回覆 JSON 格式，不要包含任何其他文字：
{
  "evaluation": "（客觀評價學生答案是否符合題目要求，如不符合請明確指出）",
  "strengths": "（如果有優點才寫，如果答案不符合要求，請寫「答案未能符合題目要求」）",
  "improvements": "（具體的改進建議，包括如何正確完成題目要求）"
}`;

    // 準備 SiliconFlow API 請求資料
    const payload = {
      model: "Qwen/Qwen2.5-7B-Instruct",
      messages: [
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    };

    const apiUrl = `https://api.siliconflow.cn/v1/chat/completions`;

    // 實作指數退避重試
    let response;
    let attempts = 0;
    const maxAttempts = 3;
    let delay = 1000;

    while (attempts < maxAttempts) {
      try {
        console.log(`Attempt ${attempts + 1} to call Gemini API`);
        
        response = await fetch(apiUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${apiKey}`
          },
          body: JSON.stringify(payload)
        });

        if (response.ok) {
          const result = await response.json();
          console.log('SiliconFlow API response received');

          if (result.choices && result.choices.length > 0 &&
              result.choices[0].message && result.choices[0].message.content) {

            const text = result.choices[0].message.content.trim();
            console.log('Parsing AI response:', text);

            try {
              // 嘗試直接解析 JSON
              const feedback = JSON.parse(text);
              return {
                statusCode: 200,
                headers: {
                  'Access-Control-Allow-Origin': '*',
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify(feedback)
              };
            } catch (parseError) {
              console.error('Failed to parse AI response as JSON:', parseError);
              console.log('Raw response text:', text);

              // 嘗試提取 JSON 部分
              const jsonMatch = text.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                try {
                  const feedback = JSON.parse(jsonMatch[0]);
                  return {
                    statusCode: 200,
                    headers: {
                      'Access-Control-Allow-Origin': '*',
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(feedback)
                  };
                } catch (secondParseError) {
                  console.error('Second JSON parse attempt failed:', secondParseError);
                }
              }

              // 如果 JSON 解析失敗，返回預設回饋
              return {
                statusCode: 200,
                headers: {
                  'Access-Control-Allow-Origin': '*',
                  'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                  evaluation: "你的答案很不錯！",
                  strengths: "句子結構完整，表達清楚。",
                  improvements: "繼續保持這樣的寫作水準！"
                })
              };
            }
          }
        } else {
          const errorText = await response.text();
          console.error(`API request failed with status ${response.status}:`, errorText);
        }
        
      } catch (error) {
        console.error(`Attempt ${attempts + 1} failed:`, error.message);
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        console.log(`Waiting ${delay}ms before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // 指數增加延遲時間
      }
    }
    
    // 所有重試都失敗，返回錯誤
    console.error('All API attempts failed');
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        error: 'AI 批改服務暫時不可用，請稍後再試',
        fallback: {
          evaluation: "暫時無法提供 AI 批改",
          strengths: "請檢查你的答案是否符合題目要求",
          improvements: "可以參考提示再次嘗試"
        }
      })
    };
    
  } catch (error) {
    console.error('Function error:', error);
    return {
      statusCode: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ 
        error: '伺服器內部錯誤',
        message: error.message 
      })
    };
  }
};
